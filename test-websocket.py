#!/usr/bin/env python3

"""
Simple WebSocket test for ECG server using Python
Tests the WebSocket connection with authentication
"""

import asyncio
import websockets
import json
import sys

# Test parameters
ECG_SERVER_URL = "ws://localhost:8000"
PATIENT_ID = "test-patient-1"
USER_ID = "Oaczetepf8kgsv5uiTJsW5Hi52dSL4BA"  # Dr<PERSON> <PERSON>'s user ID

async def test_websocket():
    wsUrl = f"{ECG_SERVER_URL}/ws/ecg/{PATIENT_ID}?token={USER_ID}"
    
    print("🔧 WebSocket Client Configuration:")
    print(f"   - Python version: {sys.version}")
    print(f"   - WebSocket library: websockets")
    print(f"   - Connection timeout: 10 seconds")
    print("")
    
    print("🔗 Testing WebSocket connection...")
    print(f"📍 URL: {wsUrl}")
    print(f"👤 User ID: {USER_ID}")
    print(f"🏥 Patient ID: {PATIENT_ID}")
    print("")
    
    try:
        # Connect to WebSocket
        async with websockets.connect(wsUrl, timeout=10) as websocket:
            print("✅ WebSocket connection opened successfully!")
            
            # Listen for messages for 5 seconds
            try:
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        print(f"📨 Received message: {data.get('type', 'unknown')} - {data.get('timestamp', 'no timestamp')}")
                        if data.get('type') == 'session_start':
                            print(f"🎯 Session started: {data.get('data', {}).get('session_id', 'no session id')}")
                        elif data.get('type') == 'signal':
                            print(f"📊 ECG signal: {data.get('data', {}).get('value', 'no value')}")
                        elif data.get('type') == 'classification':
                            classification = data.get('data', {}).get('classification', {})
                            print(f"🧠 Classification: {classification.get('class', 'unknown')} (confidence: {classification.get('confidence', 'unknown')})")
                    except json.JSONDecodeError:
                        print(f"📨 Received raw message: {message}")
                        
            except asyncio.TimeoutError:
                print("⏰ Test timeout - closing connection")
                
    except websockets.exceptions.ConnectionClosedError as e:
        print(f"🔌 WebSocket closed - Code: {e.code}, Reason: '{e.reason or 'No reason provided'}'")
        
        # Close code explanations
        close_reasons = {
            1000: 'Normal closure',
            1001: 'Going away',
            1002: 'Protocol error',
            1003: 'Unsupported data',
            1006: 'Abnormal closure (no close frame)',
            1008: 'Policy violation (likely authentication failed)',
            1009: 'Message too big',
            1010: 'Extension required',
            1011: 'Internal server error',
            1012: 'Service restart',
            1013: 'Try again later',
            1014: 'Bad gateway',
            1015: 'TLS handshake failure'
        }
        
        explanation = close_reasons.get(e.code, 'Unknown close code')
        print(f"📋 Explanation: {explanation}")
        
        return e.code != 1000
        
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket connection failed with HTTP status: {e.status_code}")
        print(f"🔍 Response headers: {dict(e.response_headers) if hasattr(e, 'response_headers') else 'Not available'}")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket error: {e}")
        print(f"🔍 Error type: {type(e).__name__}")
        return True
    
    return False

if __name__ == "__main__":
    exit_code = asyncio.run(test_websocket())
    sys.exit(1 if exit_code else 0)
