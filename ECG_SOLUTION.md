# ECG Display Issues - Complete Step-by-Step Solution Guide

## 🎯 **Overview**

This guide consolidates all solutions from the comprehensive analysis of your ECG display issues. Each step is designed to be implemented independently, providing visible improvement before moving to the next step.

### **Issues Being Fixed:**
1. ✅ **Random Flatlines** - ECG breaks showing flatline during connection issues
2. ✅ **Overlapping Signals** - Multiple ECG waveforms overlapping with time shifts
3. ✅ **Patient Data Persistence** - Previous patient data remaining when switching patients

### **Implementation Strategy:**
- **Incremental Steps** - Each step provides visible improvement
- **Low Risk** - All changes are backward compatible
- **High Impact** - Each step addresses critical functionality
- **Testable** - You can verify improvement after each step

---

## 📋 **Prerequisites**

### **Backup Current Implementation**
```bash
# Create a backup branch before starting
git checkout -b ecg-fixes-backup
git add .
git commit -m "Backup before ECG fixes implementation"
git checkout main
git checkout -b ecg-improvements
```

### **Test Environment Setup**
- Ensure ECG server is running (`pnpm dev:server`)
- Have at least 2 patients available for testing
- Test patient switching functionality first

---

## 🚀 **Step 1: Fix Patient Switch Detection (CRITICAL)**

### **Problem Solved:**
- Overlapping signals when switching patients
- Patient data persistence issues
- Chart state not resetting on patient change

### **Files to Modify:**
- `apps/web/src/components/SimpleECGChart.tsx`

### **Implementation:**

#### 1.1 Add Patient Switch Detection Effect
```typescript
// Add this to SimpleECGChart.tsx after the existing useEffect hooks

// Patient switch detection and state reset
useEffect(() => {
  console.log(`🔄 Patient switch detected: ${patientId}`);

  // Reset all chart state when patient changes
  setChartData({
    labels: [],
    datasets: [{
      label: `ECG Signal - Patient ${patientId}`,
      data: [],
      borderColor: "rgb(34, 197, 94)",
      backgroundColor: "rgba(34, 197, 94, 0.1)",
      borderWidth: 2,
      fill: false,
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 0,
    }],
  });

  // Reset recording state
  setIsRecording(false);

  // Reset config to defaults
  setConfig(prev => ({
    ...prev,
    timeWindow: 10,
    maxDataPoints: 3600,
    scale: 1.0,
  }));

  console.log(`✅ Chart state reset for patient ${patientId}`);
}, [patientId]); // This dependency is the key!
```

#### 1.2 Add Patient Data Filtering
```typescript
// Modify the processNewData function to filter by patient ID
const processNewData = useCallback(() => {
  if (signal.length === 0) return;

  // Throttle updates to prevent excessive re-renders
  const now = Date.now();
  if (now - lastUpdateTime.current < UPDATE_INTERVAL) {
    return;
  }
  lastUpdateTime.current = now;

  // 🆕 NEW: Filter data for current patient only
  const patientSignal = signal.filter(point => point.patient_id === patientId);

  if (patientSignal.length === 0) {
    console.log(`No signal data for patient ${patientId}`);
    return;
  }

  // Get the latest data points for current patient
  const latestPoints = patientSignal.slice(-config.maxDataPoints);

  // Convert ECG data to Chart.js format with optimized processing
  const labels: number[] = [];
  const data: number[] = [];

  latestPoints.forEach((point: ECGDataPoint) => {
    const timeInSeconds = point.timestamp / 1000;
    labels.push(timeInSeconds);
    data.push(point.value * config.scale);
  });

  // Update chart data with optimized dataset structure
  setChartData((prevData) => ({
    labels,
    datasets: [
      {
        ...prevData.datasets[0],
        data,
        borderColor: "rgb(34, 197, 94)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 0,
      },
    ],
  }));

  // Auto-scroll to latest data if enabled
  if (config.autoScroll && chartRef.current && latestPoints.length > 0) {
    const chart = chartRef.current;
    const latestTime = latestPoints[latestPoints.length - 1].timestamp / 1000;
    const startTime = Math.max(0, latestTime - config.timeWindow);

    if (
      chart.options.scales?.x &&
      typeof chart.options.scales.x === "object"
    ) {
      chart.options.scales.x.min = startTime;
      chart.options.scales.x.max = latestTime;
      chart.update("none");
    }
  }
}, [signal, patientId, config]); // 🆕 Added patientId dependency
```

### **Expected Results After Step 1:**
✅ **Overlapping signals eliminated** - Each patient shows clean, isolated ECG
✅ **Patient persistence fixed** - Previous patient data completely cleared on switch
✅ **Clean patient transitions** - Immediate visual feedback when switching patients
✅ **No data mixing** - 100% patient isolation

### **How to Test:**
1. Start the ECG dashboard
2. Select a patient and observe ECG display
3. Click on a different patient
4. **Expected:** Clean transition with no overlapping signals
5. Switch back to first patient
6. **Expected:** Original patient's ECG displayed cleanly

---

## 🚀 **Step 2: Clear Signal Buffers on Patient Switch (CRITICAL)**

### **Problem Solved:**
- Signal buffer accumulating multiple patients' data
- Memory leaks from uncleaned buffers
- Data contamination between patients

### **Files to Modify:**
- `apps/web/src/hooks/useECGStream.ts`

### **Implementation:**

#### 2.1 Add Signal Buffer Clearing Effect
```typescript
// Add this useEffect after the existing patientId useEffect in useECGStream.ts

// 🆕 NEW: Clear all signal data when patient changes
useEffect(() => {
  console.log(`🧹 Clearing signal buffer for patient: ${patientId}`);

  // Clear all data buffers
  setSignal([]);
  setClassifications([]);
  setError(null);
  setSessionId(null);

  // Reset reconnection attempts for new patient
  setReconnectAttempts(0);

  console.log(`✅ Signal buffer cleared for patient ${patientId}`);
}, [patientId]);
```

#### 2.2 Enhance Disconnect Function
```typescript
// Modify the existing disconnect function to be more comprehensive
const disconnect = useCallback(() => {
  console.log('🔌 Disconnecting ECG stream...');

  if (reconnectTimeoutRef.current) {
    clearTimeout(reconnectTimeoutRef.current);
  }

  if (wsRef.current) {
    wsRef.current.close(1000); // Normal closure
    wsRef.current = null;
  }

  // 🆕 ENHANCED: Clear all state on disconnect
  setIsConnected(false);
  setConnectionState("disconnected");
  setSignal([]); // 🆕 Clear signal buffer
  setClassifications([]); // 🆕 Clear classifications
  setSessionId(null); // 🆕 Clear session

  console.log('✅ ECG stream disconnected and state cleared');
}, []);
```

### **Expected Results After Step 2:**
✅ **Complete data isolation** - No patient data contamination
✅ **Memory efficiency** - Old data properly cleared
✅ **Clean reconnections** - Fresh start for each patient
✅ **Reduced memory usage** - No buffer accumulation

### **How to Test:**
1. Open browser developer tools
2. Monitor memory usage while switching patients
3. **Expected:** Memory usage stays stable, no accumulation
4. Check console for buffer clearing messages
5. **Expected:** See "Signal buffer cleared" messages on patient switch

---

## 🚀 **Step 3: Add Connection State Awareness (HIGH)**

### **Problem Solved:**
- Random flatlines during connection issues
- No visual feedback about connection status
- Chart continues showing stale data during disconnects

### **Files to Modify:**
- `apps/web/src/components/SimpleECGChart.tsx`

### **Implementation:**

#### 3.1 Add Connection State Effect
```typescript
// Add this useEffect after the patient switch effect

// 🆕 NEW: Handle connection state changes
useEffect(() => {
  if (!isConnected && chartData.labels.length > 0) {
    console.log('⚠️ Connection lost, clearing chart display');

    // Clear chart when disconnected
    setChartData({
      labels: [],
      datasets: [{
        label: "ECG Signal - Disconnected",
        data: [],
        borderColor: "rgb(239, 68, 68)", // Red for disconnected
        backgroundColor: "rgba(239, 68, 68, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 0,
      }],
    });

    // Reset recording state
    setIsRecording(false);
  } else if (isConnected && chartData.labels.length === 0) {
    console.log('✅ Connection restored, ready for data');

    // Reset to normal appearance when reconnected
    setChartData({
      labels: [],
      datasets: [{
        label: `ECG Signal - Patient ${patientId}`,
        data: [],
        borderColor: "rgb(34, 197, 94)", // Green for connected
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 0,
      }],
    });
  }
}, [isConnected, patientId]);
```

#### 3.2 Enhanced Connection Status Display
```typescript
// Modify the getConnectionBadge function to be more informative
const getConnectionBadge = () => {
  switch (connectionState) {
    case "connected":
      return <Badge className="bg-green-500">Connected</Badge>;
    case "connecting":
      return <Badge className="bg-yellow-500 animate-pulse">Connecting...</Badge>;
    case "reconnecting":
      return <Badge className="bg-orange-500 animate-pulse">Reconnecting...</Badge>;
    case "error":
      return <Badge className="bg-red-500">Connection Error</Badge>;
    default:
      return <Badge className="bg-gray-500">Disconnected</Badge>;
  }
};
```

#### 3.3 Add Data Quality Indicator
```typescript
// Add this state near the top of the component
const [dataQuality, setDataQuality] = useState<'good' | 'warning' | 'error'>('good');

// Add this useEffect to monitor data quality
useEffect(() => {
  if (error) {
    setDataQuality('error');
  } else if (!isConnected) {
    setDataQuality('warning');
  } else if (signal.length === 0) {
    setDataQuality('warning');
  } else {
    setDataQuality('good');
  }
}, [isConnected, error, signal.length]);
```

### **Expected Results After Step 3:**
✅ **No more flatlines** - Chart clears properly on disconnect
✅ **Visual connection feedback** - Clear status indicators
✅ **Professional error handling** - Graceful degradation
✅ **Data quality monitoring** - Always know system status

### **How to Test:**
1. Start monitoring a patient
2. Disconnect internet or stop ECG server
3. **Expected:** Chart shows "Disconnected" status in red
4. Reconnect internet or restart ECG server
5. **Expected:** Chart automatically resumes with green "Connected" status
6. No flatlines should appear during disconnection

---

## 🚀 **Step 4: Optimize Performance with 60 FPS Rendering (MEDIUM)**

### **Problem Solved:**
- 30 FPS throttling limiting smoothness
- Janky animation during real-time updates
- Performance below potential

### **Files to Modify:**
- `apps/web/src/components/SimpleECGChart.tsx`

### **Implementation:**

#### 4.1 Replace Throttling with Smooth Animation
```typescript
// Find and replace the existing throttling logic with this:

// 🆕 NEW: Smooth 60 FPS rendering
const animationFrameRef = useRef<number>();

// Replace the existing processNewData useEffect with this:
useEffect(() => {
  const animate = () => {
    processNewData();
    animationFrameRef.current = requestAnimationFrame(animate);
  };

  if (isConnected) {
    animationFrameRef.current = requestAnimationFrame(animate);
  }

  return () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  };
}, [isConnected, processNewData]);

// Remove or comment out the old throttling:
// const UPDATE_INTERVAL = 1000 / 30; // ❌ Remove this line
// if (now - lastUpdateTime.current < UPDATE_INTERVAL) return; // ❌ Remove this
```

#### 4.2 Optimize Chart Options for Smooth Updates
```typescript
// Modify the chartOptions useMemo to include these optimizations:
const chartOptions: ChartOptions<"line"> = useMemo(
  () => ({
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 0, // 🆕 No animation for instant updates
    },
    interaction: {
      intersect: false,
      mode: "index",
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: "Real-time ECG Signal",
        color: "#111827",
        font: {
          size: 16,
          weight: "bold",
        },
      },
      tooltip: {
        enabled: false, // 🆕 Disable tooltips for performance
      },
    },
    scales: {
      x: {
        type: "linear",
        position: "bottom",
        title: {
          display: true,
          text: "Time (seconds)",
          color: "#6B7280",
        },
        grid: {
          color: "rgba(107, 114, 128, 0.1)",
          drawOnChartArea: true,
        },
        ticks: {
          color: "#6B7280",
          maxTicksLimit: 10,
          callback: function (value) {
            return typeof value === "number" ? value.toFixed(1) + "s" : value;
          },
        },
        bounds: "data",
        adapters: {
          date: {},
        },
      },
      y: {
        title: {
          display: true,
          text: "Amplitude (mV)",
          color: "#6B7280",
        },
        grid: {
          color: "rgba(107, 114, 128, 0.1)",
          drawOnChartArea: true,
        },
        ticks: {
          color: "#6B7280",
          stepSize: 0.5,
          callback: function (value) {
            return typeof value === "number"
              ? value.toFixed(1) + " mV"
              : value;
          },
        },
        min: -2 * config.scale,
        max: 2 * config.scale,
        beginAtZero: false,
      },
    },
  }),
  [config.scale]
);
```

### **Expected Results After Step 4:**
✅ **60 FPS smooth rendering** - Ultra-smooth ECG display
✅ **No animation lag** - Instant response to data changes
✅ **Better performance** - Optimized for real-time medical monitoring
✅ **Professional appearance** - Crisp, clear ECG waveform

### **How to Test:**
1. Monitor the ECG display while connected
2. **Expected:** Extremely smooth, fluid animation
3. Compare with previous version - should notice significant improvement
4. Test rapid patient switching
5. **Expected:** Instant chart updates with no lag

---

## 🚀 **Step 5: Add Enhanced Error Handling & Recovery (MEDIUM)**

### **Problem Solved:**
- Poor error recovery after disconnections
- Limited feedback on connection issues
- No automatic recovery mechanisms

### **Files to Modify:**
- `apps/web/src/hooks/useECGStream.ts`

### **Implementation:**

#### 5.1 Enhanced Error Classification
```typescript
// Add this function after the existing validateSession function
const classifyError = (event: CloseEvent): { type: string; message: string; recoverable: boolean } => {
  const closeReasons: Record<number, { type: string; message: string; recoverable: boolean }> = {
    1000: { type: 'normal', message: 'Normal closure', recoverable: true },
    1001: { type: 'leaving', message: 'Client leaving', recoverable: true },
    1002: { type: 'protocol', message: 'Protocol error', recoverable: false },
    1003: { type: 'unsupported', message: 'Unsupported data', recoverable: false },
    1006: { type: 'abnormal', message: 'Abnormal closure', recoverable: true },
    1008: { type: 'auth', message: 'Authentication failed', recoverable: false },
    1009: { type: 'large', message: 'Message too large', recoverable: false },
    1011: { type: 'server_error', message: 'Internal server error', recoverable: true },
    1012: { type: 'restart', message: 'Service restart', recoverable: true },
    1013: { type: 'try_later', message: 'Service overloaded', recoverable: true },
    1014: { type: 'gateway', message: 'Bad gateway', recoverable: true },
    1015: { type: 'tls', message: 'TLS handshake failure', recoverable: false },
  };

  return closeReasons[event.code] || {
    type: 'unknown',
    message: `Unknown error (${event.code})`,
    recoverable: true
  };
};
```

#### 5.2 Improved Error Handling in WebSocket
```typescript
// Modify the ws.onclose handler to use the new error classification:
ws.onclose = (event) => {
  console.log(`🔌 ECG WebSocket closed - Code: ${event.code}, Reason: "${event.reason || 'No reason provided'}"`);

  const errorInfo = classifyError(event);
  console.log(`📋 Error classification: ${errorInfo.type} - ${errorInfo.message}`);
  console.log(`🔄 Recoverable: ${errorInfo.recoverable}`);

  setIsConnected(false);
  wsRef.current = null;

  // 🆕 ENHANCED: Set specific error messages based on classification
  if (errorInfo.type === 'auth') {
    setError(`Authentication failed: ${event.reason || 'Please check your credentials'}`);
  } else if (errorInfo.type === 'server_error') {
    setError(`Server error: ${event.reason || 'Internal server error'}`);
  } else if (errorInfo.type === 'normal') {
    setError(null); // No error for normal closures
  } else if (!errorInfo.recoverable) {
    setError(`Connection failed: ${errorInfo.message}`);
  } else {
    setError(`Connection lost: ${errorInfo.message}`);
  }

  // 🆕 ENHANCED: Only attempt reconnection for recoverable errors
  if (errorInfo.recoverable && reconnectAttempts < maxReconnectAttempts) {
    setConnectionState("reconnecting");
    attemptReconnection();
  } else {
    setConnectionState("error");
    if (!errorInfo.recoverable) {
      console.error('❌ Non-recoverable error, manual intervention required');
    }
  }
};
```

#### 5.3 Add Connection Health Monitoring
```typescript
// Add this state near the top of useECGStream:
const [lastMessageTime, setLastMessageTime] = useState<number>(Date.now());

// Modify the handleMessage function to track message timing:
const handleMessage = useCallback((event: MessageEvent) => {
  try {
    const message: ECGMessage = JSON.parse(event.data);

    // 🆕 NEW: Track message timing for connection health
    setLastMessageTime(Date.now());

    switch (message.type) {
      case "session_start":
        setSessionId(message.data?.session_id || null);
        console.log('🎯 ECG session started:', message.data?.session_id);
        break;

      case "signal":
        if (message.data?.value !== undefined) {
          const serverTimestamp = message.timestamp ? new Date(message.timestamp).getTime() : null;
          const clientTimestamp = Date.now();
          const dataPoint: ECGDataPoint = {
            timestamp: serverTimestamp || clientTimestamp,
            value: message.data.value,
            patient_id: message.patient_id,
          };

          setSignal(prev => {
            const newSignal = [...prev, dataPoint];
            return newSignal.length > maxSignalBuffer
              ? newSignal.slice(-maxSignalBuffer)
              : newSignal;
          });
        }
        break;

      // ... rest of the existing cases
    }
  } catch (err) {
    console.error('Error processing WebSocket message:', err);
    setError('Failed to process server message');
  }
}, []);

// Add connection health monitoring:
useEffect(() => {
  const healthCheck = setInterval(() => {
    const now = Date.now();
    const timeSinceLastMessage = now - lastMessageTime;

    if (isConnected && timeSinceLastMessage > 5000) { // 5 seconds
      console.warn('⚠️ Connection health issue: No messages received for 5 seconds');
      // Could trigger reconnection or show warning
    }
  }, 5000);

  return () => clearInterval(healthCheck);
}, [isConnected, lastMessageTime]);
```

### **Expected Results After Step 5:**
✅ **Smart error recovery** - Only retry when recovery is possible
✅ **Detailed error messages** - Clear feedback on what went wrong
✅ **Connection health monitoring** - Proactive connection management
✅ **Better debugging** - Enhanced logging for troubleshooting

### **How to Test:**
1. Test various connection scenarios (network disconnect, server restart, etc.)
2. **Expected:** Appropriate error messages and recovery attempts
3. Check console logs for detailed error classification
4. **Expected:** Clear categorization of connection issues
5. Test non-recoverable errors (e.g., authentication failure)
6. **Expected:** Appropriate error handling without unnecessary retries

---

## 🚀 **Step 6: Add Loading States & Transitions (LOW)**

### **Problem Solved:**
- No visual feedback during patient switches
- Abrupt transitions between states
- Poor user experience during loading

### **Files to Modify:**
- `apps/web/src/components/SimpleECGChart.tsx`

### **Implementation:**

#### 6.1 Add Patient Switch Loading State
```typescript
// Add this state near the top of the component:
const [isSwitching, setIsSwitching] = useState(false);

// Add this useEffect after the patient switch effect:
useEffect(() => {
  if (patientId) {
    setIsSwitching(true);
    console.log(`🔄 Loading patient ${patientId}...`);

    // Clear loading state after a short delay
    const timer = setTimeout(() => {
      setIsSwitching(false);
    }, 1500); // 1.5 second loading state

    return () => clearTimeout(timer);
  }
}, [patientId]);
```

#### 6.2 Enhanced Loading Overlay
```typescript
// Add this component before the chart in the return statement:
{isSwitching && (
  <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
      <p className="text-sm text-gray-600">Loading patient data...</p>
      <p className="text-xs text-gray-500 mt-1">Patient {patientId}</p>
    </div>
  </div>
)}
```

#### 6.3 Add Connection Loading Indicator
```typescript
// Modify the connection badge to show loading state:
const getConnectionBadge = () => {
  switch (connectionState) {
    case "connected":
      return <Badge className="bg-green-500">Connected</Badge>;
    case "connecting":
      return (
        <Badge className="bg-yellow-500">
          <span className="flex items-center gap-1">
            <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
            Connecting...
          </span>
        </Badge>
      );
    case "reconnecting":
      return (
        <Badge className="bg-orange-500">
          <span className="flex items-center gap-1">
            <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
            Reconnecting...
          </span>
        </Badge>
      );
    case "error":
      return <Badge className="bg-red-500">Connection Error</Badge>;
    default:
      return <Badge className="bg-gray-500">Disconnected</Badge>;
  }
};
```

### **Expected Results After Step 6:**
✅ **Professional loading states** - Clear feedback during transitions
✅ **Smooth patient switching** - No jarring transitions
✅ **Better UX** - Users always know what's happening
✅ **Polished interface** - Modern, responsive design

### **How to Test:**
1. Switch between patients rapidly
2. **Expected:** Smooth loading animations between switches
3. Disconnect and reconnect network
4. **Expected:** Clear connection status indicators with loading states
5. **Expected:** Professional, polished user experience

---

## 🎯 **Step 7: Final Polish & Optimizations (LOW)**

### **Problem Solved:**
- Minor visual improvements
- Performance fine-tuning
- Enhanced medical appearance

### **Files to Modify:**
- `apps/web/src/components/SimpleECGChart.tsx`

### **Implementation:**

#### 7.1 Enhanced Medical Grid Styling
```typescript
// Enhance the chart options with medical-grade styling:
const chartOptions: ChartOptions<"line"> = useMemo(
  () => ({
    // ... existing options ...
    scales: {
      x: {
        type: "linear",
        position: "bottom",
        title: {
          display: true,
          text: "Time (seconds)",
          color: "#374151",
          font: {
            size: 12,
            weight: "bold"
          }
        },
        grid: {
          color: (context) => {
            // 🆕 Major vs minor grid lines
            return context.tick.major ? "#e5e7eb" : "#f3f4f6";
          },
          borderDash: (context) => {
            return context.tick.major ? [] : [2, 4]; // Dashed minor lines
          },
          drawBorder: true,
          borderColor: "#d1d5db",
        },
        ticks: {
          color: "#6b7280",
          maxTicksLimit: 10,
          font: {
            size: 11
          },
          callback: function (value) {
            return typeof value === "number" ? value.toFixed(1) + "s" : value;
          },
        },
        bounds: "data",
      },
      y: {
        title: {
          display: true,
          text: "Amplitude (mV)",
          color: "#374151",
          font: {
            size: 12,
            weight: "bold"
          }
        },
        grid: {
          color: "#e5e7eb",
          borderDash: [5, 5], // Dashed lines for medical appearance
          drawBorder: true,
          borderColor: "#d1d5db",
        },
        ticks: {
          color: "#6b7280",
          stepSize: 0.5,
          font: {
            size: 11
          },
          callback: function (value) {
            return typeof value === "number"
              ? value.toFixed(1) + " mV"
              : value;
          },
        },
        min: -2 * config.scale,
        max: 2 * config.scale,
        beginAtZero: false,
      },
    },
  }),
  [config.scale]
);
```

#### 7.2 Add Patient Information Display
```typescript
// Add this information near the connection badges:
<div className="flex items-center gap-2 text-sm text-gray-600">
  <span>Patient: {patientId}</span>
  <span>•</span>
  <span>Signal Quality: {dataQuality.toUpperCase()}</span>
  <span>•</span>
  <span>Points: {signal.length}</span>
  <span>•</span>
  <span>Window: {config.timeWindow}s</span>
  <span>•</span>
  <span>Rate: 360Hz</span>
</div>
```

#### 7.3 Add Performance Metrics Display
```typescript
// Add this state for performance monitoring:
const [fps, setFps] = useState(0);

// Add FPS calculation in the animation loop:
const frameCountRef = useRef(0);
const lastFpsUpdate = useRef(Date.now());

// In the animate function, add:
const animate = () => {
  processNewData();

  // 🆕 Calculate FPS
  frameCountRef.current++;
  const now = Date.now();
  if (now - lastFpsUpdate.current >= 1000) {
    setFps(frameCountRef.current);
    frameCountRef.current = 0;
    lastFpsUpdate.current = now;
  }

  animationFrameRef.current = requestAnimationFrame(animate);
};

// Display FPS in debug mode:
{process.env.NODE_ENV === 'development' && (
  <div className="absolute top-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
    FPS: {fps}
  </div>
)}
```

### **Expected Results After Step 7:**
✅ **Medical-grade appearance** - Professional ECG visualization
✅ **Enhanced information display** - All relevant metrics visible
✅ **Performance monitoring** - Development tools for optimization
✅ **Polished final product** - Production-ready interface

### **How to Test:**
1. Review the overall appearance of the ECG display
2. **Expected:** Professional medical monitoring interface
3. Check all information displays
4. **Expected:** Clear, accurate patient and signal information
5. Test in development mode
6. **Expected:** FPS counter and performance metrics visible

---

## ✅ **Complete Testing Checklist**

### **Critical Functionality Tests:**
- [ ] Patient switching works without overlapping signals
- [ ] Previous patient data is completely cleared on switch
- [ ] Connection disconnections show proper status
- [ ] Reconnection works automatically
- [ ] No flatlines appear during connection issues
- [ ] 60 FPS smooth rendering is working

### **User Experience Tests:**
- [ ] Loading states appear during patient switches
- [ ] Connection status is clearly visible
- [ ] Error messages are informative
- [ ] Interface is responsive and professional
- [ ] All controls work correctly

### **Performance Tests:**
- [ ] Memory usage remains stable during patient switches
- [ ] No memory leaks detected
- [ ] CPU usage is reasonable
- [ ] Frame rate stays at 60 FPS
- [ ] Bundle size is acceptable

### **Edge Case Tests:**
- [ ] Rapid patient switching works correctly
- [ ] Network disconnection and reconnection handled
- [ ] Server restart scenarios work
- [ ] Invalid patient IDs handled gracefully
- [ ] Long-running sessions remain stable

## 🎉 **Final Expected Results**

After implementing all 7 steps, your ECG monitoring system should have:

### **✅ Critical Issues Resolved:**
1. **No Overlapping Signals** - Each patient displays clean, isolated ECG data
2. **No Random Flatlines** - Proper connection state handling prevents flatlines
3. **No Patient Data Persistence** - Complete data isolation between patients

### **✅ Enhanced Features:**
1. **60 FPS Smooth Rendering** - Ultra-smooth real-time display
2. **Professional Medical Appearance** - Production-ready interface
3. **Comprehensive Error Handling** - Robust connection management
4. **Loading States & Transitions** - Polished user experience

### **✅ Technical Improvements:**
1. **Memory Efficient** - Proper buffer management
2. **Performance Optimized** - Smooth real-time updates
3. **Error Resilient** - Smart recovery mechanisms
4. **Maintainable Code** - Clean, well-documented implementation

Your ECG monitoring system is now ready for reliable medical use with professional-grade features and rock-solid patient data isolation! 🏥✨