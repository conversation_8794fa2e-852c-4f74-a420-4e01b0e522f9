{"name": "server", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "tsx src/db/seed.ts", "db:clear": "tsx src/db/clear.ts", "db:fresh": "pnpm db:clear && pnpm db:seed"}, "dependencies": {"next": "15.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "dotenv": "^17.2.1", "@trpc/server": "^11.4.2", "@trpc/client": "^11.4.2", "drizzle-orm": "^0.44.2", "@neondatabase/serverless": "^1.0.1", "ws": "^8.18.3", "better-auth": "^1.3.4", "@better-auth/expo": "^1.3.4", "nanoid": "^5.0.6", "tsx": "^4.19.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "zod": "^4.0.13", "typescript": "^5", "drizzle-kit": "^0.31.2", "@types/ws": "^8.18.1"}}