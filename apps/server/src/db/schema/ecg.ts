import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  decimal,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { user } from "./auth";
import { doctors } from "./doctors";

export const patients = pgTable(
  "patients",
  {
    id: text("id").primaryKey(),
    userId: text("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    medicalRecordNumber: text("medical_record_number").unique(),
    dateOfBirth: timestamp("date_of_birth"),
    gender: text("gender", { enum: ["male", "female", "other"] }),
    emergencyContact: text("emergency_contact"),
    emergencyPhone: text("emergency_phone"),
    assignedDoctorId: text("assigned_doctor_id").references(() => doctors.id, {
      onDelete: "set null",
    }),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    patientUserIdx: index("idx_patient_user_id").on(table.userId),
    patientDoctorIdx: index("idx_patient_doctor_id").on(table.assignedDoctorId),
    patientMrnIdx: index("idx_patient_mrn").on(table.medicalRecordNumber),
  })
);

export const ecgSessions = pgTable(
  "ecg_sessions",
  {
    id: text("id").primaryKey(),
    patientId: text("patient_id")
      .notNull()
      .references(() => patients.id, { onDelete: "cascade" }),
    doctorId: text("doctor_id")
      .notNull()
      .references(() => doctors.id, { onDelete: "cascade" }),
    startTime: timestamp("start_time")
      .notNull()
      .default(sql`now()`),
    endTime: timestamp("end_time"),
    duration: integer("duration"), // in seconds
    sampleRate: integer("sample_rate").notNull().default(360),
    status: text("status", {
      enum: ["active", "completed", "interrupted"],
    }).default("active"),
    notes: text("notes"),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    sessionPatientIdx: index("idx_session_patient_id").on(table.patientId),
    sessionDoctorIdx: index("idx_session_doctor_id").on(table.doctorId),
    sessionStatusIdx: index("idx_session_status").on(table.status),
    sessionStartTimeIdx: index("idx_session_start_time").on(table.startTime),
  })
);

export const ecgClassifications = pgTable(
  "ecg_classifications",
  {
    id: text("id").primaryKey(),
    sessionId: text("session_id")
      .notNull()
      .references(() => ecgSessions.id, { onDelete: "cascade" }),
    timestamp: timestamp("timestamp")
      .notNull()
      .default(sql`now()`),
    classification: text("classification").notNull(),
    confidence: decimal("confidence", { precision: 5, scale: 4 }).notNull(),
    isAbnormal: boolean("is_abnormal").notNull().default(false),
    alertSent: boolean("alert_sent").notNull().default(false),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    classificationSessionIdx: index("idx_classification_session_id").on(
      table.sessionId
    ),
    classificationTimestampIdx: index("idx_classification_timestamp").on(
      table.timestamp
    ),
    classificationAbnormalIdx: index("idx_classification_abnormal").on(
      table.isAbnormal
    ),
    classificationAlertIdx: index("idx_classification_alert").on(
      table.alertSent
    ),
  })
);