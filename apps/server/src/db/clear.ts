import { config } from "dotenv";
import { db } from "./index";
import { sql } from "drizzle-orm";

config();

async function clear() {
  console.log("🧹 Clearing database tables...");
  try {
    // Use TRUNCATE with CASCADE to clear in one shot respecting FK dependencies
    // Order doesn't matter with CASCADE, but we include all known tables explicitly for clarity
    await db.execute(sql`
      TRUNCATE TABLE 
        password_reset_tokens,
        organization_invitations,
        doctors,
        account,
        session,
        verification,
        "user",
        organizations
      RESTART IDENTITY CASCADE
    `);

    console.log("✅ All tables cleared.");
  } catch (error) {
    console.error("❌ Failed to clear tables:", error);
    process.exit(1);
  }
}

clear().then(() => process.exit(0));


