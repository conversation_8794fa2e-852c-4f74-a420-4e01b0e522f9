import { config } from "dotenv";
import { db, patients, user } from "./index";
import { eq } from "drizzle-orm";

// Load environment variables
config();

async function checkPatients() {
  console.log("🔍 Checking default patients in database...");

  try {
    // Get all patients with their user information
    const allPatients = await db
      .select({
        id: patients.id,
        userId: patients.userId,
        medicalRecordNumber: patients.medicalRecordNumber,
        dateOfBirth: patients.dateOfBirth,
        gender: patients.gender,
        emergencyContact: patients.emergencyContact,
        emergencyPhone: patients.emergencyPhone,
        assignedDoctorId: patients.assignedDoctorId,
        userName: user.name,
        userEmail: user.email,
        userRole: user.role,
      })
      .from(patients)
      .leftJoin(user, eq(patients.userId, user.id));

    console.log(`\n📋 Found ${allPatients.length} patients:\n`);

    allPatients.forEach((patient, index) => {
      console.log(`${index + 1}. ${patient.userName} (${patient.userEmail})`);
      console.log(`   Patient ID: ${patient.id}`);
      console.log(`   MRN: ${patient.medicalRecordNumber}`);
      console.log(`   Gender: ${patient.gender}`);
      console.log(`   DOB: ${patient.dateOfBirth?.toISOString().split('T')[0]}`);
      console.log(`   Emergency Contact: ${patient.emergencyContact}`);
      console.log(`   Emergency Phone: ${patient.emergencyPhone}`);
      console.log(`   Assigned Doctor ID: ${patient.assignedDoctorId}`);
      console.log(`   User Role: ${patient.userRole}`);
      console.log("");
    });

    // Highlight the test patient
    const testPatient = allPatients.find(p => 
      p.medicalRecordNumber === "MRN-TEST-001" || 
      p.id === "test-patient-1" ||
      p.userName?.includes("Test Patient ECG")
    );

    if (testPatient) {
      console.log("🎯 Test Patient Found:");
      console.log(`   Name: ${testPatient.userName}`);
      console.log(`   ID: ${testPatient.id}`);
      console.log(`   MRN: ${testPatient.medicalRecordNumber}`);
      console.log(`   Email: ${testPatient.userEmail}`);
    } else {
      console.log("⚠️  Test patient not found!");
    }

    // Check for hardcoded MRNs
    console.log("\n🔍 Checking for hardcoded MRNs:");
    const hardcodedMRNs = ["MRN001", "MRN002", "MRN003", "MRN-TEST-001"];
    
    hardcodedMRNs.forEach(mrn => {
      const patient = allPatients.find(p => p.medicalRecordNumber === mrn);
      if (patient) {
        console.log(`   ✅ ${mrn}: ${patient.userName} (${patient.userEmail})`);
      } else {
        console.log(`   ❌ ${mrn}: Not found`);
      }
    });

  } catch (error) {
    console.error("❌ Error checking patients:", error);
  }
}

// Run the check
checkPatients().then(() => {
  console.log("\n✅ Patient check completed!");
  process.exit(0);
}).catch((error) => {
  console.error("💥 Patient check failed:", error);
  process.exit(1);
});
