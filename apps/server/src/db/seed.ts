import { config } from "dotenv";
import { db, user, organizations, doctors, organizationInvitations, patients, ecgSessions, ecgClassifications } from "./index";
import { nanoid } from "nanoid";
import { auth } from "../lib/auth";
import { eq } from "drizzle-orm";

// Load environment variables
config();

// Test password for all seeded users
const TEST_PASSWORD = "password123";

async function seed() {
  console.log("🌱 Seeding database...");

  try {
    // 1. Create Organizations
    console.log("Creating organizations...");
    const org1 = await db.insert(organizations).values({
      id: nanoid(),
      name: "City General Hospital",
      adminEmail: "<EMAIL>",
      address: "123 Medical Center Dr, City, State 12345",
      phone: "******-0123",
      website: "https://citygeneral.org",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    const org2 = await db.insert(organizations).values({
      id: nanoid(),
      name: "Heart Care Specialists",
      adminEmail: "<EMAIL>",
      address: "456 Cardiology Ave, City, State 12345",
      phone: "******-0456",
      website: "https://heartcare.org",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    // 2. Create Admin Users using Better Auth signup
    console.log("Creating admin users...");

    const admin1Result = await auth.api.signUpEmail({
      body: {
        name: "Dr. Sarah Johnson",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update admin1 with proper role and status
    if (admin1Result.user) {
      await db.update(user)
        .set({
          role: "admin",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, admin1Result.user.id));
    }

    const admin2Result = await auth.api.signUpEmail({
      body: {
        name: "Dr. Michael Chen",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update admin2 with proper role and status
    if (admin2Result.user) {
      await db.update(user)
        .set({
          role: "admin",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, admin2Result.user.id));
    }

    // 3. Create Doctor Users using Better Auth signup
    console.log("Creating doctor users...");

    const doctor1Result = await auth.api.signUpEmail({
      body: {
        name: "Dr. Emily Rodriguez",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update doctor1 with proper role and status
    if (doctor1Result.user) {
      await db.update(user)
        .set({
          role: "doctor",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, doctor1Result.user.id));
    }

    const doctor2Result = await auth.api.signUpEmail({
      body: {
        name: "Dr. James Wilson",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update doctor2 with proper role and status
    if (doctor2Result.user) {
      await db.update(user)
        .set({
          role: "doctor",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, doctor2Result.user.id));
    }

    const doctor3Result = await auth.api.signUpEmail({
      body: {
        name: "Dr. Lisa Park",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update doctor3 with proper role and status
    if (doctor3Result.user) {
      await db.update(user)
        .set({
          role: "doctor",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, doctor3Result.user.id));
    }

    // 4. Create Doctor Profiles
    console.log("Creating doctor profiles...");
    const doctorProfiles = [];

    if (doctor1Result.user) {
      doctorProfiles.push({
        id: nanoid(),
        userId: doctor1Result.user.id,
        organizationId: org1[0].id,
        employeeId: "CGH001",
        department: "Cardiology",
        specialization: "Interventional Cardiology",
        phone: "******-0101",
        licenseNumber: "MD123456",
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    if (doctor2Result.user) {
      doctorProfiles.push({
        id: nanoid(),
        userId: doctor2Result.user.id,
        organizationId: org1[0].id,
        employeeId: "CGH002",
        department: "Emergency Medicine",
        specialization: "Emergency Cardiology",
        phone: "******-0102",
        licenseNumber: "MD123457",
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    if (doctor3Result.user) {
      doctorProfiles.push({
        id: nanoid(),
        userId: doctor3Result.user.id,
        organizationId: org2[0].id,
        employeeId: "HCS001",
        department: "Cardiology",
        specialization: "Electrophysiology",
        phone: "******-0201",
        licenseNumber: "MD123458",
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    if (doctorProfiles.length > 0) {
      await db.insert(doctors).values(doctorProfiles);
    }

    // 5. Create Patient Users using Better Auth signup
    console.log("Creating patient users...");

    const patient1Result = await auth.api.signUpEmail({
      body: {
        name: "John Smith",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update patient1 with proper role and status
    if (patient1Result.user) {
      await db.update(user)
        .set({
          role: "patient",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, patient1Result.user.id));
    }

    const patient2Result = await auth.api.signUpEmail({
      body: {
        name: "Maria Garcia",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update patient2 with proper role and status
    if (patient2Result.user) {
      await db.update(user)
        .set({
          role: "patient",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, patient2Result.user.id));
    }

    const patient3Result = await auth.api.signUpEmail({
      body: {
        name: "Robert Johnson",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update patient3 with proper role and status
    if (patient3Result.user) {
      await db.update(user)
        .set({
          role: "patient",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, patient3Result.user.id));
    }

    // 6. Create Patient Profiles
    console.log("Creating patient profiles...");
    const patientProfiles = [];
    const createdDoctors = await db.select().from(doctors);

    if (patient1Result.user && createdDoctors.length > 0) {
      patientProfiles.push({
        id: nanoid(),
        userId: patient1Result.user.id,
        medicalRecordNumber: "MRN001",
        dateOfBirth: new Date("1985-03-15"),
        gender: "male" as const,
        emergencyContact: "Jane Smith (Wife)",
        emergencyPhone: "******-1001",
        assignedDoctorId: createdDoctors[0].id, // Dr. Emily Rodriguez
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    if (patient2Result.user && createdDoctors.length > 1) {
      patientProfiles.push({
        id: nanoid(),
        userId: patient2Result.user.id,
        medicalRecordNumber: "MRN002",
        dateOfBirth: new Date("1978-07-22"),
        gender: "female" as const,
        emergencyContact: "Carlos Garcia (Husband)",
        emergencyPhone: "******-1002",
        assignedDoctorId: createdDoctors[1].id, // Dr. James Wilson
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    if (patient3Result.user && createdDoctors.length > 2) {
      patientProfiles.push({
        id: nanoid(),
        userId: patient3Result.user.id,
        medicalRecordNumber: "MRN003",
        dateOfBirth: new Date("1962-11-08"),
        gender: "male" as const,
        emergencyContact: "Susan Johnson (Wife)",
        emergencyPhone: "******-1003",
        assignedDoctorId: createdDoctors[2].id, // Dr. Lisa Park
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // Add test patient for ECG WebSocket testing
    const testPatientResult = await auth.api.signUpEmail({
      body: {
        name: "Test Patient ECG",
        email: "<EMAIL>",
        password: TEST_PASSWORD,
      },
    });

    // Update test patient with proper role and status
    if (testPatientResult.user) {
      await db.update(user)
        .set({
          role: "patient",
          accountStatus: "active",
          emailVerified: true,
          firstLogin: false,
        })
        .where(eq(user.id, testPatientResult.user.id));
    }

    // Add test patient profile with specific ID for ECG testing
    if (testPatientResult.user && createdDoctors.length > 0) {
      patientProfiles.push({
        id: "test-patient-1", // Specific ID for ECG WebSocket testing
        userId: testPatientResult.user.id,
        medicalRecordNumber: "MRN-TEST-001",
        dateOfBirth: new Date("1990-01-01"),
        gender: "male" as const,
        emergencyContact: "Emergency Contact",
        emergencyPhone: "******-9999",
        assignedDoctorId: createdDoctors[0].id, // Assign to Dr. Emily Rodriguez
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    let createdPatients: any[] = [];
    if (patientProfiles.length > 0) {
      createdPatients = await db.insert(patients).values(patientProfiles).returning();
    }

    // 7. Create Sample ECG Sessions
    console.log("Creating sample ECG sessions...");
    const ecgSessionsData = [];

    if (createdPatients.length > 0 && createdDoctors.length > 0) {
      // Create a completed ECG session for patient 1
      const session1Id = nanoid();
      ecgSessionsData.push({
        id: session1Id,
        patientId: createdPatients[0].id,
        doctorId: createdDoctors[0].id,
        startTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        endTime: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
        duration: 3600, // 1 hour in seconds
        sampleRate: 360,
        status: "completed" as const,
        notes: "Routine ECG monitoring - normal rhythm observed",
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      });

      // Create an active ECG session for patient 2
      const session2Id = nanoid();
      ecgSessionsData.push({
        id: session2Id,
        patientId: createdPatients[1].id,
        doctorId: createdDoctors[1].id,
        startTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        endTime: null,
        duration: null,
        sampleRate: 360,
        status: "active" as const,
        notes: "Ongoing monitoring after chest pain complaint",
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        updatedAt: new Date(),
      });
    }

    let createdSessions: any[] = [];
    if (ecgSessionsData.length > 0) {
      createdSessions = await db.insert(ecgSessions).values(ecgSessionsData).returning();
    }

    // 8. Create Sample ECG Classifications
    console.log("Creating sample ECG classifications...");
    const classificationsData = [];

    if (createdSessions.length > 0) {
      // Create classifications for the completed session
      const completedSession = createdSessions.find(s => s.status === "completed");
      if (completedSession) {
        // Normal rhythm classification
        classificationsData.push({
          id: nanoid(),
          sessionId: completedSession.id,
          timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
          classification: "Normal Sinus Rhythm",
          confidence: "0.9650",
          isAbnormal: false,
          alertSent: false,
          createdAt: new Date(Date.now() - 1.5 * 60 * 60 * 1000),
        });

        // Another normal classification
        classificationsData.push({
          id: nanoid(),
          sessionId: completedSession.id,
          timestamp: new Date(Date.now() - 1.2 * 60 * 60 * 1000), // 1.2 hours ago
          classification: "Normal Sinus Rhythm",
          confidence: "0.9420",
          isAbnormal: false,
          alertSent: false,
          createdAt: new Date(Date.now() - 1.2 * 60 * 60 * 1000),
        });
      }

      // Create classifications for the active session
      const activeSession = createdSessions.find(s => s.status === "active");
      if (activeSession) {
        // Recent abnormal classification
        classificationsData.push({
          id: nanoid(),
          sessionId: activeSession.id,
          timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
          classification: "Atrial Fibrillation",
          confidence: "0.8750",
          isAbnormal: true,
          alertSent: true,
          createdAt: new Date(Date.now() - 5 * 60 * 1000),
        });
      }
    }

    if (classificationsData.length > 0) {
      await db.insert(ecgClassifications).values(classificationsData);
    }

    // 9. Create Organization Invitations (for testing)
    console.log("Creating organization invitations...");
    if (admin1Result.user) {
      await db.insert(organizationInvitations).values({
        id: nanoid(),
        organizationId: org1[0].id,
        email: "<EMAIL>",
        invitedBy: admin1Result.user.id,
        role: "doctor",
        token: nanoid(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        createdAt: new Date(),
      });
    }

    console.log("✅ Database seeded successfully!");
    console.log("\n📊 Created:");
    console.log("- 2 Organizations");
    console.log("- 2 Admin users with passwords");
    console.log("- 3 Doctor users with profiles and passwords");
    console.log("- 4 Patient users with passwords and medical profiles");
    console.log("- 2 Sample ECG sessions (1 completed, 1 active)");
    console.log("- 3 ECG classifications (2 normal, 1 abnormal)");
    console.log("- 1 Organization invitation");

    console.log("\n🔑 Test Credentials (Password: " + TEST_PASSWORD + "):");
    console.log("Admin 1: <EMAIL>");
    console.log("Admin 2: <EMAIL>");
    console.log("Doctor 1: <EMAIL>");
    console.log("Doctor 2: <EMAIL>");
    console.log("Doctor 3: <EMAIL>");
    console.log("Patient 1: <EMAIL>");
    console.log("Patient 2: <EMAIL>");
    console.log("Patient 3: <EMAIL>");
    console.log("Test Patient: <EMAIL> (ID: test-patient-1, assigned to Dr. Emily)");

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

// Run the seed function
seed()
  .then(() => {
    console.log("🎉 Seeding completed!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Seeding failed:", error);
    process.exit(1);
  });
