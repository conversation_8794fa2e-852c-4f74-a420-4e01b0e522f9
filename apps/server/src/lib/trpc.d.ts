export declare const t: import("@trpc/server").TRPCRootObject<{
    session: {
        session: {
            id: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            token: string;
            ipAddress?: string | null | undefined | undefined;
            userAgent?: string | null | undefined | undefined;
        };
        user: {
            id: string;
            email: string;
            emailVerified: boolean;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined;
            role: string | null | undefined;
            accountStatus: string | null | undefined;
            firstLogin: boolean | null | undefined;
        };
    } | null;
}, object, import("@trpc/server").TRPCRuntimeConfigOptions<{
    session: {
        session: {
            id: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            token: string;
            ipAddress?: string | null | undefined | undefined;
            userAgent?: string | null | undefined | undefined;
        };
        user: {
            id: string;
            email: string;
            emailVerified: boolean;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined;
            role: string | null | undefined;
            accountStatus: string | null | undefined;
            firstLogin: boolean | null | undefined;
        };
    } | null;
}, object>, {
    ctx: {
        session: {
            session: {
                id: string;
                userId: string;
                expiresAt: Date;
                createdAt: Date;
                updatedAt: Date;
                token: string;
                ipAddress?: string | null | undefined | undefined;
                userAgent?: string | null | undefined | undefined;
            };
            user: {
                id: string;
                email: string;
                emailVerified: boolean;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                image?: string | null | undefined | undefined;
                role: string | null | undefined;
                accountStatus: string | null | undefined;
                firstLogin: boolean | null | undefined;
            };
        } | null;
    };
    meta: object;
    errorShape: import("@trpc/server").TRPCDefaultErrorShape;
    transformer: false;
}>;
export declare const router: import("@trpc/server").TRPCRouterBuilder<{
    ctx: {
        session: {
            session: {
                id: string;
                userId: string;
                expiresAt: Date;
                createdAt: Date;
                updatedAt: Date;
                token: string;
                ipAddress?: string | null | undefined | undefined;
                userAgent?: string | null | undefined | undefined;
            };
            user: {
                id: string;
                email: string;
                emailVerified: boolean;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                image?: string | null | undefined | undefined;
                role: string | null | undefined;
                accountStatus: string | null | undefined;
                firstLogin: boolean | null | undefined;
            };
        } | null;
    };
    meta: object;
    errorShape: import("@trpc/server").TRPCDefaultErrorShape;
    transformer: false;
}>;
export declare const publicProcedure: import("@trpc/server").TRPCProcedureBuilder<{
    session: {
        session: {
            id: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            token: string;
            ipAddress?: string | null | undefined | undefined;
            userAgent?: string | null | undefined | undefined;
        };
        user: {
            id: string;
            email: string;
            emailVerified: boolean;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined;
            role: string | null | undefined;
            accountStatus: string | null | undefined;
            firstLogin: boolean | null | undefined;
        };
    } | null;
}, object, object, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, false>;
export declare const protectedProcedure: import("@trpc/server").TRPCProcedureBuilder<{
    session: {
        session: {
            id: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            token: string;
            ipAddress?: string | null | undefined | undefined;
            userAgent?: string | null | undefined | undefined;
        };
        user: {
            id: string;
            email: string;
            emailVerified: boolean;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined;
            role: string | null | undefined;
            accountStatus: string | null | undefined;
            firstLogin: boolean | null | undefined;
        };
    } | null;
}, object, {
    session: {
        session: {
            id: string;
            userId: string;
            expiresAt: Date;
            createdAt: Date;
            updatedAt: Date;
            token: string;
            ipAddress?: string | null | undefined | undefined;
            userAgent?: string | null | undefined | undefined;
        };
        user: {
            id: string;
            email: string;
            emailVerified: boolean;
            name: string;
            createdAt: Date;
            updatedAt: Date;
            image?: string | null | undefined | undefined;
            role: string | null | undefined;
            accountStatus: string | null | undefined;
            firstLogin: boolean | null | undefined;
        };
    };
}, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, import("@trpc/server").TRPCUnsetMarker, false>;
