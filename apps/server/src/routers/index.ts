import { protectedProcedure, publicProcedure, router } from "../lib/trpc";
import { patientRouter } from "./patients";

export const appRouter = router({
	healthCheck: publicProcedure.query(() => {
		return "OK";
	}),
	privateData: protectedProcedure.query(({ ctx }) => {
		return {
			message: "This is private",
			user: ctx.session.user,
		};
	}),
	patients: patientRouter,
});
export type AppRouter = typeof appRouter;
