import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
	// Handle CORS preflight requests
	if (request.method === "OPTIONS") {
		return new NextResponse(null, {
			status: 200,
			headers: {
				"Access-Control-Allow-Origin": process.env.CORS_ORIGIN || "http://localhost:3001",
				"Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
				"Access-Control-Allow-Headers": "Content-Type, Authorization, Cookie",
				"Access-Control-Allow-Credentials": "true",
			},
		});
	}

	// Handle regular requests
	const res = NextResponse.next();

	res.headers.set("Access-Control-Allow-Origin", process.env.CORS_ORIGIN || "http://localhost:3001");
	res.headers.set("Access-Control-Allow-Credentials", "true");
	res.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
	res.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, Cookie");

	return res;
}

export const config = {
	matcher: "/:path*",
};
