import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";

/**
 * Custom endpoint for external services (like ECG server) to validate sessions
 * Uses the recommended Better Auth auth.api.getSession() approach with automatic cookie handling
 */
export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Session validation request received");
    
    // ✨ MUCH SIMPLER: Just use the request headers directly!
    // The browser will automatically include the better-auth.session_token cookie
    // when credentials: 'include' is used
    
    console.log("🍪 Cookies from request:", request.headers.get('cookie'));
    
    // Use Better Auth's recommended session validation (per documentation)
    const session = await auth.api.getSession({
      headers: request.headers, // ✅ Pass actual request headers with cookies
      query: {
        disableCookieCache: true // Force fresh validation from database
      }
    });
    
    console.log(`🔍 Session result:`, session ? 'Valid session found' : 'No valid session');
    
    if (!session || !session.user) {
      console.log("❌ No valid session found");
      return NextResponse.json(
        { error: "Invalid or expired session" }, 
        { status: 401 }
      );
    }

    // Return session data in the format expected by ECG server
    return NextResponse.json({
      user: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
        accountStatus: session.user.accountStatus
      },
      session: {
        id: session.session.id,
        userId: session.user.id,
        expiresAt: session.session.expiresAt
      }
    });

  } catch (error) {
    console.error("Session validation error:", error);
    
    return NextResponse.json(
      { error: "Session validation failed" }, 
      { status: 500 }
    );
  }
}

// Also support GET method with automatic cookie handling
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 GET session validation request received");
    console.log("🍪 Cookies from GET request:", request.headers.get('cookie'));
    
    // Use Better Auth's recommended session validation with request headers
    const session = await auth.api.getSession({
      headers: request.headers, // ✅ Automatic cookie handling
      query: {
        disableCookieCache: true
      }
    });
    
    console.log(`🔍 GET Session result:`, session ? 'Valid session found' : 'No valid session');

    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Invalid or expired session" }, 
        { status: 401 }
      );
    }

    // Return session data
    return NextResponse.json({
      user: {
        id: session.user.id,
        name: session.user.name,
        email: session.user.email,
        role: session.user.role,
        accountStatus: session.user.accountStatus
      },
      session: {
        id: session.session.id,
        userId: session.user.id,
        expiresAt: session.session.expiresAt
      }
    });

  } catch (error) {
    console.error("Session validation error:", error);
    
    return NextResponse.json(
      { error: "Session validation failed" }, 
      { status: 500 }
    );
  }
}