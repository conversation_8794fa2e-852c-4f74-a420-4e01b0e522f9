import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";

/**
 * Debug endpoint to understand how Better Auth sessions work
 */
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Debug session request");
    console.log("Headers:", Object.fromEntries(request.headers.entries()));
    
    // Try to get session using actual request headers (how it should work)
    const session = await auth.api.getSession({
      headers: request.headers,
      query: {
        disableCookieCache: true
      }
    });
    
    console.log("Session found:", !!session);
    if (session) {
      console.log("User:", session.user?.email);
    }
    
    return NextResponse.json({
      hasSession: !!session,
      user: session?.user ? {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        role: session.user.role
      } : null,
      sessionId: session?.session?.id,
      cookies: Object.fromEntries(
        request.headers.get('cookie')?.split(';').map(cookie => {
          const [name, value] = cookie.trim().split('=');
          return [name, value];
        }) || []
      )
    });
    
  } catch (error) {
    console.error("Debug session error:", error);
    
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      hasSession: false
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();
    console.log(`🔍 Debug with token: ${token?.substring(0, 10)}...`);
    
    // Try different cookie names that might be used
    const cookieVariations = [
      `better-auth.session_token=${token}`,
      `__Secure-better-auth.session_token=${token}`,
      `session_token=${token}`,
      `auth_session=${token}`
    ];
    
    const results: Record<string, any> = {};
    
    for (const cookieFormat of cookieVariations) {
      try {
        const headers = new Headers();
        headers.set('cookie', cookieFormat);
        
        const session = await auth.api.getSession({
          headers: headers,
          query: { disableCookieCache: true }
        });
        
        results[cookieFormat.split('=')[0]] = {
          success: !!session,
          user: session?.user?.email || null
        };
      } catch (error) {
        results[cookieFormat.split('=')[0]] = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
      }
    }
    
    return NextResponse.json({
      token: token?.substring(0, 10) + '...',
      results
    });
    
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}