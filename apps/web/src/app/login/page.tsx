"use client"

import SignInForm from "@/components/sign-in-form";
import HealthLinkBrandPanel from "@/components/health-link-brand-panel";

export default function LoginPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        <SignInForm />
      </div>
      
      {/* Desktop Layout */}
      <div className="hidden lg:grid lg:grid-cols-5 lg:h-screen lg:p-[45px]">
        {/* Left Brand Panel - spans 2 columns */}
        <aside className="col-span-2" aria-label="Health Link branding">
          <HealthLinkBrandPanel />
        </aside>
        
        {/* Right Form Panel - spans 3 columns */}
        <section className="col-span-3 flex items-center justify-center" aria-label="Sign in form">
          <div className="w-full max-w-[702px] px-[120px]">
            <SignInForm />
          </div>
        </section>
      </div>
    </main>
  );
}
