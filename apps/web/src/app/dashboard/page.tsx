"use client";

import { authClient } from "@/lib/auth-client";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Activity,
  Users,
  Clock,
  TrendingUp,
  Heart,
  Stethoscope,
  FileText,
  Settings,
  ChevronRight,
} from "lucide-react";

/**
 * Main HealthLink Dashboard
 *
 * Provides overview of medical monitoring capabilities and navigation
 * to specific monitoring tools like ECG visualization.
 */
export default function Dashboard() {
  const router = useRouter();
  const { data: session, isPending } = authClient.useSession();
  const [currentTime, setCurrentTime] = useState(new Date());

  const privateData = useQuery(trpc.privateData.queryOptions());

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    if (!session && !isPending) {
      router.push("/login");
    }
  }, [session, isPending]);

  if (isPending) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-700">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  // Navigation to ECG monitoring
  const navigateToECG = () => {
    console.log("🔄 Navigating to ECG dashboard...");
    router.push("/dashboard/ecg");
  };

  // Quick stats for the dashboard
  const quickStats = [
    {
      title: "Active Patients",
      value: "2",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "ECG Sessions Today",
      value: "5",
      icon: Activity,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Monitoring Time",
      value: "2.5h",
      icon: Clock,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "System Status",
      value: "Online",
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
    },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            HealthLink Dashboard
          </h1>
          <p className="text-gray-800 mt-1">
            Welcome back, Dr. {session.user.name}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-800">
            {currentTime.toLocaleDateString()}
          </p>
          <p className="text-lg font-semibold text-gray-900">
            {currentTime.toLocaleTimeString()}
          </p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-800">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <IconComponent className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* ECG Monitoring */}
        <Card
          className="hover:shadow-lg transition-shadow cursor-pointer"
          onClick={() => {
            console.log("🖱️ ECG Card clicked");
            navigateToECG();
          }}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-red-50">
                  <Heart className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <CardTitle className="text-lg text-gray-900 font-semibold">
                    ECG Monitoring
                  </CardTitle>
                  <p className="text-sm text-gray-800">
                    Real-time cardiac monitoring
                  </p>
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">Chart Options:</span>
                <div className="flex gap-1">
                  <Badge className="bg-blue-500">Chart.js</Badge>
                  <Badge className="bg-green-500">LightningChart</Badge>
                  <Badge className="bg-purple-500">Plotly</Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">Active Patients:</span>
                <span className="text-sm font-semibold text-gray-900">
                  2 assigned
                </span>
              </div>
              <Button
                className="w-full mt-4 bg-red-500 hover:bg-red-600"
                onClick={(e) => {
                  console.log("🖱️ Start ECG Monitoring button clicked");
                  e.stopPropagation();
                  navigateToECG();
                }}
              >
                <Activity className="h-4 w-4 mr-2" />
                Start ECG Monitoring
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Patient Management */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-3 rounded-full bg-blue-50">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg text-gray-900 font-semibold">
                    Patient Management
                  </CardTitle>
                  <p className="text-sm text-gray-800">
                    Manage assigned patients
                  </p>
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">
                  Assigned Patients:
                </span>
                <span className="text-sm font-semibold text-gray-900">
                  2 active
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-800">Recent Sessions:</span>
                <span className="text-sm font-semibold text-gray-900">
                  5 today
                </span>
              </div>
              <Button
                variant="outline"
                className="w-full mt-4 text-gray-900 hover:text-gray-900"
              >
                <Users className="h-4 w-4 mr-2" />
                View Patients
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-gray-900 font-semibold">
            <FileText className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-green-100">
                  <Activity className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    ECG Session Completed
                  </p>
                  <p className="text-xs text-gray-800">
                    Patient: John Smith (MRN001) - 30 minutes
                  </p>
                </div>
              </div>
              <span className="text-xs text-gray-800">2 hours ago</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-blue-100">
                  <Stethoscope className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    New Patient Assigned
                  </p>
                  <p className="text-xs text-gray-800">
                    Patient: Test Patient ECG (MRN-TEST-001)
                  </p>
                </div>
              </div>
              <span className="text-xs text-gray-800">4 hours ago</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-purple-100">
                  <Settings className="h-4 w-4 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    System Update
                  </p>
                  <p className="text-xs text-gray-800">
                    Chart.js ECG implementation optimized
                  </p>
                </div>
              </div>
              <span className="text-xs text-gray-800">6 hours ago</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gray-900 font-semibold">
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={() => {
                console.log("🖱️ Quick Actions ECG Monitor button clicked");
                navigateToECG();
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              <Heart className="h-4 w-4 mr-2" />
              ECG Monitor
            </Button>
            <Button
              variant="outline"
              className="text-gray-900 hover:text-gray-900"
            >
              <Users className="h-4 w-4 mr-2" />
              Patient List
            </Button>
            <Button
              variant="outline"
              className="text-gray-900 hover:text-gray-900"
            >
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <div className="text-center text-sm text-gray-500 py-4">
        <p>
          HealthLink Medical Monitoring System • ECG Server:{" "}
          <span className="text-green-600 font-semibold">Online</span> •
          Authentication:{" "}
          <span className="text-green-600 font-semibold">Active</span>
        </p>
      </div>
    </div>
  );
}
