"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { authClient } from "@/lib/auth-client";
import { trpc } from "@/utils/trpc";
import { useRouter } from "next/navigation";
import SimpleECGChart from "@/components/SimpleECGChart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, Home } from "lucide-react";
import type { PatientInfo, ECGSession } from "@/types/ecg";

/**
 * ECG Monitoring Dashboard
 *
 * This page provides real-time ECG monitoring capabilities for doctors.
 * Features:
 * - Patient selection from assigned patients
 * - Real-time ECG visualization
 * - Session history and management
 * - Classification results display
 */
export default function ECGDashboard() {
  const router = useRouter();
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [recentSessions, setRecentSessions] = useState<ECGSession[]>([]);
  const [debugInfo, setDebugInfo] = useState<{
    cookieCount: number;
    availableCookies: string;
    isHydrated: boolean;
  }>({
    cookieCount: 0,
    availableCookies: "Loading...",
    isHydrated: false,
  });

  const { data: session } = authClient.useSession();

  // Fetch assigned patients using tRPC
  const {
    data: patientsData,
    isLoading: patientsLoading,
    error: patientsError,
    refetch: refetchPatients,
  } = useQuery({
    ...trpc.patients.getAssignedPatients.queryOptions(),
    enabled: !!session?.user,
  }) as {
    data:
      | {
          patients: Array<{
            id: string;
            name: string;
            medicalRecordNumber: string | null;
            dateOfBirth: Date | null;
            gender: string | null;
            emergencyContact: string | null;
            emergencyPhone: string | null;
          }>;
          doctorId: string;
        }
      | undefined;
    isLoading: boolean;
    error: any;
    refetch: () => void;
  };

  // Convert tRPC data to our PatientInfo format
  const patients: PatientInfo[] = patientsData?.patients
    ? patientsData.patients.map((patient) => ({
        id: patient.id,
        name: patient.name,
        medical_record_number: patient.medicalRecordNumber || "",
        date_of_birth: patient.dateOfBirth
          ? typeof patient.dateOfBirth === "string"
            ? patient.dateOfBirth
            : (patient.dateOfBirth as Date).toISOString()
          : undefined,
        gender: (patient.gender as "male" | "female" | "other") || undefined,
        emergency_contact: patient.emergencyContact || undefined,
        emergency_phone: patient.emergencyPhone || undefined,
        assigned_doctor_id: patientsData.doctorId,
      }))
    : [];

  // Auto-select patient with MRN-TEST-001 if available, otherwise first patient
  useEffect(() => {
    if (!selectedPatientId && patients.length > 0) {
      // Debug: Log all patients to see their MRNs
      console.log("🔍 All patients:", patients.map(p => ({ 
        name: p.name, 
        mrn: p.medical_record_number,
        id: p.id 
      })));
      
      // Look for the test patient first by MRN or name
      const testPatient = patients.find(p => 
        p.medical_record_number === "MRN-TEST-001" || 
        p.name.includes("Test Patient ECG")
      );
      
      if (testPatient) {
        console.log("🎯 Auto-selecting test patient:", testPatient.id, testPatient.name, "MRN:", testPatient.medical_record_number);
        setSelectedPatientId(testPatient.id);
      } else {
        console.log("🎯 Test patient not found, auto-selecting first patient:", patients[0].id, patients[0].name);
        setSelectedPatientId(patients[0].id);
      }
    }
  }, [patients, selectedPatientId]);

  // Fallback to test patient if no patients are loaded
  useEffect(() => {
    if (!selectedPatientId && !patientsLoading && patients.length === 0) {
      console.log("🧪 Using test patient ID for debugging");
      setSelectedPatientId("QqGq_lQX8-IMQqSjE_fPV");
    }
  }, [selectedPatientId, patientsLoading, patients.length]);

  // Debug: Log patient selection and loading state
  console.log("🏥 Dashboard Debug:", {
    selectedPatientId,
    patientsCount: patients?.length || 0,
    patients: patients?.map((p) => ({ id: p.id, name: p.name })) || [],
    patientsLoading,
    patientsError: patientsError?.message || null,
    patientsData,
    sessionUser: session?.user?.id || null,
  });

  /**
   * Fetches recent ECG sessions (mock data for now)
   */
  const fetchRecentSessions = async () => {
    try {
      // For now, we'll use mock data since the ECG server doesn't have this endpoint yet
      // In a real implementation, this would fetch from the ECG server or tRPC
      const mockSessions: ECGSession[] = [
        {
          id: "session_1",
          patient_id: "patient_1",
          doctor_id: session?.user?.id || "doctor_1",
          start_time: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          end_time: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
          duration: 1800, // 30 minutes
          sample_rate: 360,
          status: "completed",
          notes: "Routine monitoring session",
          created_at: new Date(Date.now() - 3600000).toISOString(),
          updated_at: new Date(Date.now() - 1800000).toISOString(),
        },
        {
          id: "session_2",
          patient_id: "patient_2",
          doctor_id: session?.user?.id || "doctor_1",
          start_time: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          end_time: new Date(Date.now() - 5400000).toISOString(), // 1.5 hours ago
          duration: 1800, // 30 minutes
          sample_rate: 360,
          status: "completed",
          created_at: new Date(Date.now() - 7200000).toISOString(),
          updated_at: new Date(Date.now() - 5400000).toISOString(),
        },
      ];

      setRecentSessions(mockSessions);
    } catch (err) {
      console.error("Error fetching recent sessions:", err);
    }
  };

  // Initialize recent sessions (mock data for now)
  useEffect(() => {
    fetchRecentSessions();
  }, []);

  // Populate debug info after client-side hydration to prevent SSR mismatch
  useEffect(() => {
    setDebugInfo({
      cookieCount: document.cookie.split(";").filter((c) => c.trim()).length,
      availableCookies:
        document.cookie
          .split(";")
          .map((c) => c.trim().split("=")[0])
          .filter((name) => name)
          .join(", ") || "None",
      isHydrated: true,
    });
  }, []);

  if (patientsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-800">Loading ECG Dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-96">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">
                Authentication Required
              </h3>
              <p className="text-gray-800 mb-4">
                Please log in to access the ECG dashboard.
              </p>

              {/* Debug info */}
              <div className="text-xs text-gray-700 mb-4 p-2 bg-gray-50 rounded">
                <p>
                  <strong>Debug Info:</strong>
                </p>
                <p>Session: {session ? "Found" : "Not found"}</p>
                <p>Cookies: {debugInfo.cookieCount} total</p>
                <p>Available: {debugInfo.availableCookies}</p>
                <p>Hydrated: {debugInfo.isHydrated ? "Yes" : "No"}</p>
              </div>

              <Button
                onClick={() => (window.location.href = "/login")}
                className="w-full"
              >
                Go to Login
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 text-sm text-gray-700">
        <button
          onClick={() => router.push("/dashboard")}
          className="flex items-center hover:text-blue-600 transition-colors"
        >
          <Home className="h-4 w-4 mr-1" />
          Dashboard
        </button>
        <ChevronRight className="h-4 w-4" />
        <span className="text-gray-900 font-medium">ECG Monitoring</span>
      </nav>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">ECG Monitoring Dashboard</h1>
          <p className="text-gray-800 mt-1">
            Welcome, Dr. {session.user.name} • Real-time cardiac monitoring
          </p>
        </div>
        <Badge variant="outline" className="text-sm">
          {patients.length} Assigned Patients
        </Badge>
      </div>

      {/* Error Display */}
      {patientsError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">
              ⚠️ Failed to load patients: {patientsError.message}
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 text-gray-900 hover:text-gray-900"
              onClick={() => refetchPatients()}
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Patient List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-gray-900 font-semibold">
              Assigned Patients
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {patients.length === 0 ? (
              <div className="text-center py-8 text-gray-700">
                <p>No patients assigned</p>
                <p className="text-sm mt-1">
                  Contact your administrator to assign patients
                </p>
              </div>
            ) : (
              patients.slice().reverse().map((patient) => (
                <div
                  key={patient.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedPatientId === patient.id
                      ? "bg-blue-50 border-blue-200"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => setSelectedPatientId(patient.id)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{patient.name}</p>
                      <p className="text-sm text-gray-800">
                        MRN: {patient.medical_record_number}
                      </p>
                      {patient.date_of_birth && (
                        <p className="text-xs text-gray-700">
                          DOB:{" "}
                          {new Date(patient.date_of_birth).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-xs text-gray-700">Available</span>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* ECG Monitor */}
        <div className="lg:col-span-2">
          {!session?.user ? (
            <Card className="h-96">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-8 h-8 text-red-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <p className="text-red-500 text-lg">
                    Authentication Required
                  </p>
                  <p className="text-red-400 text-sm mt-1">
                    Please log in to access ECG monitoring
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4 text-gray-900 hover:text-gray-900"
                    onClick={() => (window.location.href = "/login")}
                  >
                    Go to Login
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : selectedPatientId ? (
            <div className="space-y-4">
              {/* ECG Visualization */}
              <SimpleECGChart
                patientId={selectedPatientId}
                height={400}
                showControls={true}
                className="w-full"
              />
            </div>
          ) : (
            <Card className="h-96">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg
                      className="w-8 h-8 text-gray-600">
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                  </div>
                  <p className="text-gray-700 text-lg">
                    Select a patient to view ECG monitoring
                  </p>
                  <p className="text-gray-700 text-sm mt-1">
                    Choose from your assigned patients on the left
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gray-900 font-semibold">
            Recent ECG Sessions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentSessions.length === 0 ? (
            <div className="text-center py-8 text-gray-700">
              <p>No recent ECG sessions</p>
              <p className="text-sm mt-1">
                Start monitoring a patient to see session history
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b text-gray-900 font-semibold">
                    <th className="text-left p-2 text-gray-900">Patient</th>
                    <th className="text-left p-2 text-gray-900">Start Time</th>
                    <th className="text-left p-2 text-gray-900">Duration</th>
                    <th className="text-left p-2 text-gray-900">Status</th>
                    <th className="text-left p-2 text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentSessions.map((session) => {
                    const patient = patients.find(
                      (p) => p.id === session.patient_id
                    );
                    return (
                      <tr key={session.id} className="border-b text-gray-800">
                        <td className="p-2 text-gray-900 font-medium">
                          {patient?.name || `Patient ${session.patient_id}`}
                        </td>
                        <td className="p-2 text-gray-800">
                          {new Date(session.start_time).toLocaleString()}
                        </td>
                        <td className="p-2 text-gray-800">
                          {session.duration
                            ? `${Math.round(session.duration / 60)}m`
                            : "Ongoing"}
                        </td>
                        <td className="p-2">
                          <Badge
                            variant={
                              session.status === "active"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {session.status}
                          </Badge>
                        </td>
                        <td className="p-2">
                          <Button variant="outline" size="sm" className="text-gray-900 hover:text-gray-900">
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
