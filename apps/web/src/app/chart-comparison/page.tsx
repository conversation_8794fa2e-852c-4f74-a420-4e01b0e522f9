"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Import all chart components
import ECGChart from "@/components/ECGChart";
import LightningECGChart from "@/components/LightningECGChart";
import ChartJSECGChart from "@/components/ChartJSECGChart";
import PlotlyECGChart from "@/components/PlotlyECGChart";
import ObservablePlotECGChart from "@/components/ObservablePlotECGChart";

const chartOptions = [
  {
    id: "canvas",
    name: "Canvas (Current)",
    component: ECGChart,
    description: "Custom Canvas-based implementation",
    pros: ["✅ No dependencies", "✅ Full control", "✅ Lightweight", "✅ Fast"],
    cons: ["❌ Manual implementation", "❌ Limited features"],
    license: "Free",
    performance: "Good",
    color: "bg-blue-50 border-blue-200",
  },
  {
    id: "lightningchart",
    name: "LightningChart JS",
    component: LightningECGChart,
    description: "Professional WebGL-accelerated charting",
    pros: ["✅ WebGL acceleration", "✅ Medical-grade", "✅ High performance", "✅ Professional features"],
    cons: ["❌ License required", "❌ Large bundle"],
    license: "Commercial",
    performance: "Excellent",
    color: "bg-purple-50 border-purple-200",
  },
  {
    id: "chartjs",
    name: "Chart.js",
    component: ChartJSECGChart,
    description: "Popular open-source charting library",
    pros: ["✅ Free & open source", "✅ Large community", "✅ Good documentation", "✅ Responsive"],
    cons: ["❌ Canvas-based", "❌ Performance limits"],
    license: "MIT (Free)",
    performance: "Good",
    color: "bg-green-50 border-green-200",
  },
  {
    id: "plotly",
    name: "Plotly.js",
    component: PlotlyECGChart,
    description: "Scientific plotting with WebGL support",
    pros: ["✅ Free & open source", "✅ WebGL support", "✅ Scientific features", "✅ Interactive"],
    cons: ["❌ Large bundle size", "❌ Complex API"],
    license: "MIT (Free)",
    performance: "Excellent",
    color: "bg-orange-50 border-orange-200",
  },
  {
    id: "observable",
    name: "Observable Plot",
    component: ObservablePlotECGChart,
    description: "Grammar of graphics by D3 team",
    pros: ["✅ Free & open source", "✅ Modern API", "✅ Grammar of graphics", "✅ Lightweight"],
    cons: ["❌ Newer library", "❌ Learning curve"],
    license: "ISC (Free)",
    performance: "Very Good",
    color: "bg-teal-50 border-teal-200",
  },
];

export default function ChartComparisonPage() {
  const [selectedChart, setSelectedChart] = useState("canvas");
  const [showAll, setShowAll] = useState(false);

  const selectedOption = chartOptions.find(option => option.id === selectedChart);
  const SelectedComponent = selectedOption?.component || ECGChart;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">📊 ECG Chart Library Comparison</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Compare different charting libraries for real-time ECG visualization. 
          Each option has different trade-offs in terms of performance, features, and licensing.
        </p>
      </div>

      {/* Chart Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Chart Library Selection
            <Button
              onClick={() => setShowAll(!showAll)}
              variant="outline"
              size="sm"
            >
              {showAll ? "Show Single" : "Show All"}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedChart} onValueChange={setSelectedChart}>
            <TabsList className="grid w-full grid-cols-5">
              {chartOptions.map((option) => (
                <TabsTrigger key={option.id} value={option.id} className="text-xs">
                  {option.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>

      {/* Comparison Table */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Feature Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border border-gray-200 p-3 text-left">Library</th>
                  <th className="border border-gray-200 p-3 text-left">License</th>
                  <th className="border border-gray-200 p-3 text-left">Performance</th>
                  <th className="border border-gray-200 p-3 text-left">Bundle Size</th>
                  <th className="border border-gray-200 p-3 text-left">Best For</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="border border-gray-200 p-3 font-medium">Canvas (Current)</td>
                  <td className="border border-gray-200 p-3">
                    <Badge variant="secondary">Free</Badge>
                  </td>
                  <td className="border border-gray-200 p-3">Good</td>
                  <td className="border border-gray-200 p-3">~5KB</td>
                  <td className="border border-gray-200 p-3">Custom requirements</td>
                </tr>
                <tr>
                  <td className="border border-gray-200 p-3 font-medium">LightningChart JS</td>
                  <td className="border border-gray-200 p-3">
                    <Badge variant="destructive">Commercial</Badge>
                  </td>
                  <td className="border border-gray-200 p-3">Excellent</td>
                  <td className="border border-gray-200 p-3">~2MB</td>
                  <td className="border border-gray-200 p-3">Professional/Medical</td>
                </tr>
                <tr>
                  <td className="border border-gray-200 p-3 font-medium">Chart.js</td>
                  <td className="border border-gray-200 p-3">
                    <Badge variant="default">MIT</Badge>
                  </td>
                  <td className="border border-gray-200 p-3">Good</td>
                  <td className="border border-gray-200 p-3">~200KB</td>
                  <td className="border border-gray-200 p-3">General purpose</td>
                </tr>
                <tr>
                  <td className="border border-gray-200 p-3 font-medium">Plotly.js</td>
                  <td className="border border-gray-200 p-3">
                    <Badge variant="default">MIT</Badge>
                  </td>
                  <td className="border border-gray-200 p-3">Excellent</td>
                  <td className="border border-gray-200 p-3">~3MB</td>
                  <td className="border border-gray-200 p-3">Scientific/Interactive</td>
                </tr>
                <tr>
                  <td className="border border-gray-200 p-3 font-medium">Observable Plot</td>
                  <td className="border border-gray-200 p-3">
                    <Badge variant="default">ISC</Badge>
                  </td>
                  <td className="border border-gray-200 p-3">Very Good</td>
                  <td className="border border-gray-200 p-3">~100KB</td>
                  <td className="border border-gray-200 p-3">Data visualization</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Chart Display */}
      {showAll ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {chartOptions.map((option) => {
            const Component = option.component;
            return (
              <div key={option.id} className={`border-2 rounded-lg p-4 ${option.color}`}>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold">{option.name}</h3>
                  <p className="text-sm text-gray-600">{option.description}</p>
                  <div className="flex gap-2 mt-2">
                    <Badge variant="outline">{option.license}</Badge>
                    <Badge variant="outline">{option.performance}</Badge>
                  </div>
                </div>
                <Component 
                  patientId="test-patient-1" 
                  height={300} 
                  showControls={false}
                />
              </div>
            );
          })}
        </div>
      ) : (
        selectedOption && (
          <div className="space-y-6">
            {/* Selected Chart Details */}
            <Card className={selectedOption.color}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {selectedOption.name}
                  <div className="flex gap-2">
                    <Badge variant="outline">{selectedOption.license}</Badge>
                    <Badge variant="outline">{selectedOption.performance}</Badge>
                  </div>
                </CardTitle>
                <p className="text-gray-600">{selectedOption.description}</p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <h4 className="font-semibold text-green-700 mb-2">Advantages</h4>
                    <ul className="space-y-1">
                      {selectedOption.pros.map((pro, index) => (
                        <li key={index} className="text-sm">{pro}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-700 mb-2">Considerations</h4>
                    <ul className="space-y-1">
                      {selectedOption.cons.map((con, index) => (
                        <li key={index} className="text-sm">{con}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Chart Demo */}
            <SelectedComponent 
              patientId="test-patient-1" 
              height={400} 
              showControls={true}
            />
          </div>
        )
      )}

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>🎯 Recommendations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg bg-green-50">
              <h4 className="font-semibold text-green-800">For Development</h4>
              <p className="text-sm text-green-700 mt-2">
                <strong>Chart.js</strong> or <strong>Canvas</strong> - Free, reliable, good performance
              </p>
            </div>
            <div className="p-4 border rounded-lg bg-blue-50">
              <h4 className="font-semibold text-blue-800">For Production</h4>
              <p className="text-sm text-blue-700 mt-2">
                <strong>Plotly.js</strong> - Free, WebGL acceleration, professional features
              </p>
            </div>
            <div className="p-4 border rounded-lg bg-purple-50">
              <h4 className="font-semibold text-purple-800">For Medical/Enterprise</h4>
              <p className="text-sm text-purple-700 mt-2">
                <strong>LightningChart</strong> - Medical-grade, highest performance
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
