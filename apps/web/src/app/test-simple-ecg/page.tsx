"use client";

import { useState } from "react";
import SimpleECGChart from "@/components/SimpleECGChart";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

/**
 * Test page for the Simple Chart.js ECG Chart
 * 
 * This page demonstrates the Chart.js-based ECG visualization
 * following the approach from the Medium article:
 * https://medium.com/@pavanbhute/live-streaming-data-in-react-using-node-js-websocket-and-chart-js-3ea4d4ceac8f
 */
export default function TestSimpleECG() {
  const [testPatientId, setTestPatientId] = useState<string>("test-patient-1");

  const testPatients = [
    { id: "test-patient-1", name: "Test Patient 1 (VTach)", condition: "Ventricular Tachycardia" },
    { id: "QqGq_lQX8-IMQqSjE_fPV", name: "Test Patient 2 (Normal)", condition: "Normal Sinus Rhythm" },
    { id: "test-patient-3", name: "Test Patient 3 (AFib)", condition: "Atrial Fibrillation" },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Simple Chart.js ECG Test</h1>
          <p className="text-gray-600 mt-2">
            Testing Chart.js-based ECG visualization with live streaming data
          </p>
        </div>
        <Badge className="bg-blue-500">Chart.js Implementation</Badge>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Test Patient:</label>
            <select 
              value={testPatientId}
              onChange={(e) => setTestPatientId(e.target.value)}
              className="px-3 py-2 border rounded-lg"
            >
              {testPatients.map(patient => (
                <option key={patient.id} value={patient.id}>
                  {patient.name} - {patient.condition}
                </option>
              ))}
            </select>
          </div>

          <div className="text-sm text-gray-600">
            <p>✅ <strong>No licensing issues</strong> - Chart.js is open source</p>
            <p>✅ <strong>Simple implementation</strong> - Based on proven Medium article approach</p>
            <p>✅ <strong>Real-time streaming</strong> - WebSocket integration with Better Auth</p>
          </div>
        </CardContent>
      </Card>

      {/* Chart.js Benefits */}
      <Card>
        <CardHeader>
          <CardTitle>Chart.js Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">✅</div>
              <div className="text-sm text-gray-600">No License Issues</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">📈</div>
              <div className="text-sm text-gray-600">Proven Reliable</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">🚀</div>
              <div className="text-sm text-gray-600">Easy to Maintain</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simple ECG Chart */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Chart.js ECG Visualization</h2>
          <div className="flex items-center gap-2">
            <Badge className="bg-green-500">Open Source</Badge>
            <Badge className="bg-blue-500">No License Required</Badge>
            <Badge className="bg-purple-500">Real-time</Badge>
          </div>
        </div>

        <SimpleECGChart
          patientId={testPatientId}
          height={500}
          showControls={true}
          className="w-full"
        />
      </div>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Implementation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">Chart.js Features</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Open source MIT license</li>
                <li>• Canvas-based rendering</li>
                <li>• Real-time data updates</li>
                <li>• Responsive design</li>
                <li>• Medical-grade precision</li>
                <li>• Smooth animations (disabled for performance)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Integration Benefits</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Better Auth authentication preserved</li>
                <li>• WebSocket streaming compatible</li>
                <li>• Patient-doctor access control maintained</li>
                <li>• Simple maintenance and updates</li>
                <li>• No licensing costs or complexity</li>
                <li>• Proven reliability in production</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold mb-2">Data Flow (Medium Article Pattern)</h4>
            <div className="text-sm text-gray-600">
              <code className="bg-white px-2 py-1 rounded">
                WebSocket → useECGStream → Chart.js Line Component → Canvas Rendering
              </code>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Chart Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Feature</th>
                  <th className="text-left p-2">Chart.js (Simple)</th>
                  <th className="text-left p-2">LightningChart (Enhanced)</th>
                  <th className="text-left p-2">Plotly (Standard)</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="p-2 font-medium">License</td>
                  <td className="p-2 text-green-600">✅ Open Source (MIT)</td>
                  <td className="p-2 text-red-600">❌ Commercial License Required</td>
                  <td className="p-2 text-green-600">✅ Open Source (MIT)</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2 font-medium">Performance</td>
                  <td className="p-2 text-yellow-600">⚡ Good (Canvas)</td>
                  <td className="p-2 text-green-600">🚀 Excellent (WebGL)</td>
                  <td className="p-2 text-yellow-600">⚡ Good (SVG/Canvas)</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2 font-medium">Complexity</td>
                  <td className="p-2 text-green-600">✅ Simple</td>
                  <td className="p-2 text-red-600">❌ Complex</td>
                  <td className="p-2 text-yellow-600">⚡ Medium</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2 font-medium">Maintenance</td>
                  <td className="p-2 text-green-600">✅ Easy</td>
                  <td className="p-2 text-red-600">❌ License Management</td>
                  <td className="p-2 text-green-600">✅ Easy</td>
                </tr>
                <tr>
                  <td className="p-2 font-medium">Real-time</td>
                  <td className="p-2 text-green-600">✅ Excellent</td>
                  <td className="p-2 text-green-600">✅ Excellent</td>
                  <td className="p-2 text-green-600">✅ Good</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="space-y-2">
            <h4 className="font-semibold">1. Authentication Test</h4>
            <p className="text-sm text-gray-600">
              Ensure you're logged in as a doctor (e.g., <EMAIL>) 
              to test the authentication integration.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">2. WebSocket Connection</h4>
            <p className="text-sm text-gray-600">
              The chart will connect to the ECG server at localhost:8000. 
              Ensure the ECG server is running for real-time data streaming.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">3. Chart Controls</h4>
            <p className="text-sm text-gray-600">
              Use the chart controls to test recording, scaling, time window adjustments. 
              All features work without any licensing issues.
            </p>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">4. Performance Testing</h4>
            <p className="text-sm text-gray-600">
              Monitor the chart performance with real-time data. Chart.js provides 
              smooth rendering for medical ECG visualization needs.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <div className="text-center text-sm text-gray-500 py-4">
        Simple ECG Chart powered by Chart.js • 
        Authenticated via Better Auth • Real-time via WebSocket • 
        Based on <a href="https://medium.com/@pavanbhute/live-streaming-data-in-react-using-node-js-websocket-and-chart-js-3ea4d4ceac8f" 
                   className="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">
          Medium Article Pattern
        </a>
      </div>
    </div>
  );
}
