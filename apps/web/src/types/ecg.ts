/**
 * ECG TypeScript interfaces and types for real-time streaming
 * 
 * These types define the data structures used for ECG WebSocket communication,
 * component props, and data visualization in the HealthLink frontend.
 */

// Core ECG data structures
export interface ECGDataPoint {
  timestamp: number;
  value: number;
  patient_id: string;
}

export interface ClassificationResult {
  class: string;
  confidence: number;
  is_abnormal: boolean;
  timestamp: number;
  patient_id: string;
}

// WebSocket message types
export interface ECGMessage {
  type: "signal" | "classification" | "session_start" | "session_end" | "error";
  timestamp: string; // ISO timestamp
  patient_id: string;
  data?: {
    value?: number;
    classification?: {
      class: string;
      confidence: number;
      is_abnormal: boolean;
    };
    session_id?: string;
    error_message?: string;
  };
}

// ECG streaming configuration
export interface ECGStreamConfig {
  sample_rate: number;
  buffer_size: number;
  classification_interval: number; // seconds
}

// Session and patient information
export interface ECGSession {
  id: string;
  patient_id: string;
  doctor_id: string;
  start_time: string;
  end_time?: string;
  duration?: number; // in seconds
  sample_rate: number;
  status: "active" | "completed" | "interrupted";
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface PatientInfo {
  id: string;
  name: string;
  medical_record_number: string;
  date_of_birth?: string;
  gender?: "male" | "female" | "other";
  emergency_contact?: string;
  emergency_phone?: string;
  assigned_doctor_id?: string;
}

// Hook return types
export interface UseECGStreamReturn {
  signal: ECGDataPoint[];
  classifications: ClassificationResult[];
  isConnected: boolean;
  error: string | null;
  connectionState: ECGConnectionState;
  reconnectAttempts: number;
  sessionId: string | null;
  // Advanced buffer methods
  getCurrentSignal: () => ECGDataPoint[];
  getSignalTimeWindow: (windowSeconds: number) => ECGDataPoint[];
  getBufferStats: () => {
    sampleRate: number;
    dataSpanSeconds: number;
    memoryEfficiency: number;
    capacity: number;
    size: number;
    memoryUsageBytes: number;
    utilizationPercent: number;
  };
}

export type ECGConnectionState = 
  | "disconnected" 
  | "connecting" 
  | "connected" 
  | "reconnecting" 
  | "error";

// Component props
export interface ECGChartProps {
  patientId: string;
  height?: number;
  showControls?: boolean;
  className?: string;
}

export interface ECGDashboardProps {
  initialPatientId?: string;
}

// Chart configuration and display options
export interface ECGChartConfig {
  scale: number;
  speed: number;
  showGrid: boolean;
  gridColor: string;
  waveformColor: string;
  backgroundColor: string;
  timeWindow: number; // seconds of data to display
}

// Real-time data buffer management
export interface ECGBuffer {
  data: ECGDataPoint[];
  maxSize: number;
  sampleRate: number;
}

// Error types specific to ECG streaming
export interface ECGError {
  type: "connection" | "authentication" | "data" | "server";
  message: string;
  timestamp: string;
  patient_id?: string;
  session_id?: string;
}

// WebSocket connection options
export interface ECGWebSocketOptions {
  url: string;
  patientId: string;
  token: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  maxReconnectDelay?: number;
}

// Dashboard state management
export interface ECGDashboardState {
  selectedPatientId: string | null;
  patients: PatientInfo[];
  recentSessions: ECGSession[];
  isLoading: boolean;
  error: string | null;
}

// Classification display types
export interface ClassificationDisplay {
  classification: ClassificationResult;
  isLatest: boolean;
  showAlert: boolean;
}

// Chart drawing context and utilities
export interface ECGCanvasContext {
  canvas: HTMLCanvasElement;
  ctx: CanvasRenderingContext2D;
  width: number;
  height: number;
  config: ECGChartConfig;
}

// Animation and rendering types
export interface ECGAnimationFrame {
  timestamp: number;
  dataPoints: ECGDataPoint[];
  timeIndicatorPosition: number;
}

// All types are already exported above with their definitions
