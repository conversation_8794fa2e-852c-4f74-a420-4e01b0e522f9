"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { authClient } from "@/lib/auth-client";
import type {
  ECGDataPoint,
  ClassificationResult,
  ECGMessage,
  UseECGStreamReturn,
  ECGConnectionState,
} from "@/types/ecg";

/**
 * Custom hook for managing real-time ECG data streaming via WebSocket
 * 
 * This hook handles:
 * - WebSocket connection management with authentication
 * - Real-time ECG data processing and buffering
 * - Connection state management and error handling
 * - Automatic reconnection with exponential backoff
 * - Session validation using Better Auth
 * 
 * @param patientId - The ID of the patient to stream ECG data for
 * @returns ECG streaming state and data
 */
export function useECGStream(patientId: string): UseECGStreamReturn {
  // State management
  const [signal, setSignal] = useState<ECGDataPoint[]>([]);
  const [classifications, setClassifications] = useState<ClassificationResult[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [connectionState, setConnectionState] = useState<ECGConnectionState>("disconnected");
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Refs for managing WebSocket and timers
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const maxReconnectAttempts = 5;
  const maxSignalBuffer = 3600; // Keep last 10 seconds at 360Hz

  // Get session from authClient instead of cookies
  const { data: session } = authClient.useSession();

  /**
   * Validates the current session using Better Auth
   * Uses the improved approach with credentials: 'include'
   */
  const validateSession = useCallback(async () => {
    try {
      const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000';
      const response = await fetch(`${serverUrl}/api/auth/validate-session`, {
        method: 'POST',
        credentials: 'include', // ✅ Use improved Better Auth approach
      });

      if (!response.ok) {
        throw new Error('Session validation failed');
      }

      const sessionData = await response.json();
      return sessionData;
    } catch (err) {
      console.error('Session validation error:', err);
      throw new Error('Authentication required');
    }
  }, []);

  /**
   * Gets a temporary authentication token by validating the current session
   * This works around the cross-origin cookie issue by using the session validation endpoint
   */
  const getAuthToken = useCallback(async () => {
    console.log('🔐 Getting auth token via session validation...');

    if (!session?.user) {
      console.error('❌ No active session found');
      throw new Error('No active session found. Please ensure you are logged in.');
    }

    try {
      // Validate session and get a temporary token
      const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3000';
      const response = await fetch(`${serverUrl}/api/auth/validate-session`, {
        method: 'POST',
        credentials: 'include', // Include cookies for cross-origin requests
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Session validation failed: ${response.status}`);
      }

      const sessionData = await response.json();
      console.log(`✅ Session validated successfully`);
      console.log(`👤 User: ${sessionData.user.name} (${sessionData.user.email})`);
      console.log(`🏥 Role: ${sessionData.user.role}`);
      console.log(`🆔 User ID: ${sessionData.user.id}`);

      // Return the user ID as the auth token
      return sessionData.user.id;
    } catch (error) {
      console.error('❌ Failed to get auth token:', error);
      throw new Error('Failed to validate session for WebSocket authentication');
    }
  }, [session]);

  /**
   * Processes incoming WebSocket messages
   */
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      const message: ECGMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case "session_start":
          setSessionId(message.data?.session_id || null);
          console.log('🎯 ECG session started:', message.data?.session_id);
          break;
          
        case "signal":
          if (message.data?.value !== undefined) {
            // Use server timestamp if available, otherwise fall back to client timestamp
            const serverTimestamp = message.timestamp ? new Date(message.timestamp).getTime() : null;
            const clientTimestamp = Date.now();
            const dataPoint: ECGDataPoint = {
              timestamp: serverTimestamp || clientTimestamp,
              value: message.data.value,
              patient_id: message.patient_id,
            };

            // Debug timing consistency (only log occasionally to avoid spam)
            if (Math.random() < 0.001) { // Log ~0.1% of messages
              const timeDiff = serverTimestamp ? Math.abs(serverTimestamp - clientTimestamp) : 0;
              console.log(`🕐 Timing debug: Server=${serverTimestamp}, Client=${clientTimestamp}, Diff=${timeDiff}ms`);
            }

            setSignal(prev => {
              const newSignal = [...prev, dataPoint];
              // Keep buffer size manageable
              return newSignal.length > maxSignalBuffer
                ? newSignal.slice(-maxSignalBuffer)
                : newSignal;
            });
          }
          break;
          
        case "classification":
          if (message.data?.classification) {
            const classification: ClassificationResult = {
              class: message.data.classification.class,
              confidence: message.data.classification.confidence,
              is_abnormal: message.data.classification.is_abnormal,
              timestamp: Date.now(),
              patient_id: message.patient_id,
            };
            
            setClassifications(prev => [...prev, classification]);
            
            // Log abnormal classifications
            if (classification.is_abnormal) {
              console.warn('🚨 Abnormal ECG classification detected:', classification);
            }
          }
          break;
          
        case "error":
          const errorMsg = message.data?.error_message || 'Unknown server error';
          setError(errorMsg);
          console.error('❌ ECG server error:', errorMsg);
          break;
          
        case "session_end":
          console.log('🏁 ECG session ended');
          setConnectionState("disconnected");
          break;
          
        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (err) {
      console.error('Error processing WebSocket message:', err);
      setError('Failed to process server message');
    }
  }, []); // Remove maxSignalBuffer from dependencies

  /**
   * Establishes WebSocket connection with authentication
   */
  const connectToStream = useCallback(async () => {
    try {
      setConnectionState("connecting");
      setError(null);

      console.log('🔗 Starting ECG WebSocket connection...');

      // Validate session first
      console.log('🔐 Validating session...');
      await validateSession();
      console.log('✅ Session validation successful');

      // Get the auth token for authentication
      console.log('🔐 Getting auth token...');
      const authToken = await getAuthToken();
      console.log('✅ Auth token retrieved successfully');
      
      // Create WebSocket connection with authentication
      const ecgServerUrl = process.env.NEXT_PUBLIC_ECG_SERVER_URL || 'ws://localhost:8000';
      const wsUrl = `${ecgServerUrl}/ws/ecg/${patientId}?token=${authToken}`;

      console.log(`🔗 Connecting to WebSocket: ${wsUrl}`);
      console.log(`🎯 Patient ID: ${patientId}`);
      console.log(`🔑 Auth Token: ${authToken.substring(0, 8)}...`);

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('✅ ECG WebSocket connected successfully for patient:', patientId);
        setIsConnected(true);
        setConnectionState("connected");
        setReconnectAttempts(0);
        setError(null);
      };

      ws.onmessage = handleMessage;

      ws.onclose = (event) => {
        console.log(`🔌 ECG WebSocket closed - Code: ${event.code}, Reason: "${event.reason || 'No reason provided'}"`);

        // Provide detailed close code explanations
        const closeReasons: Record<number, string> = {
          1000: 'Normal closure',
          1001: 'Going away',
          1002: 'Protocol error',
          1003: 'Unsupported data',
          1006: 'Abnormal closure (no close frame)',
          1008: 'Policy violation (likely authentication failed)',
          1009: 'Message too big',
          1010: 'Extension required',
          1011: 'Internal server error',
          1012: 'Service restart',
          1013: 'Try again later',
          1014: 'Bad gateway',
          1015: 'TLS handshake failure'
        };

        const closeExplanation = closeReasons[event.code] || 'Unknown close code';
        console.log(`📋 Close explanation: ${closeExplanation}`);

        setIsConnected(false);
        wsRef.current = null;

        // Set appropriate error message based on close code
        if (event.code === 1008) {
          setError(`Authentication failed: ${event.reason || 'Invalid credentials'}`);
        } else if (event.code === 1011) {
          setError(`Server error: ${event.reason || 'Internal server error'}`);
        } else if (event.code !== 1000) {
          setError(`Connection closed: ${closeExplanation} (${event.code})`);
        }

        // Only attempt reconnection if not a normal closure
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          setConnectionState("reconnecting");
          attemptReconnection();
        } else {
          setConnectionState("disconnected");
        }
      };

      ws.onerror = (error) => {
        console.error('❌ ECG WebSocket error occurred');
        console.error('🔍 Error details:', {
          type: error.type,
          target: (error.target as WebSocket)?.readyState,
          url: wsUrl,
          timestamp: new Date().toISOString()
        });

        // WebSocket errors don't provide detailed error information
        // The actual error details will be in the onclose event
        console.log('⏳ Waiting for close event for detailed error information...');

        setError('WebSocket connection error - check console for details');
        setConnectionState("error");
        setIsConnected(false);
      };

    } catch (err) {
      console.error('Failed to connect to ECG stream:', err);
      setError(err instanceof Error ? err.message : 'Connection failed');
      setConnectionState("error");
      setIsConnected(false);
    }
  }, [patientId, session?.user?.id]); // Simplified dependencies

  /**
   * Attempts reconnection with exponential backoff
   */
  const attemptReconnection = useCallback(() => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      setError("Maximum reconnection attempts reached");
      setConnectionState("error");
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Max 30 seconds
    setReconnectAttempts(prev => prev + 1);
    
    console.log(`🔄 Attempting reconnection ${reconnectAttempts + 1}/${maxReconnectAttempts} in ${delay}ms`);
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connectToStream();
    }, delay);
  }, [reconnectAttempts]); // Remove connectToStream to break circular dependency

  /**
   * Disconnects from the WebSocket stream
   */
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close(1000); // Normal closure
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setConnectionState("disconnected");
    setSessionId(null);
  }, []);

  // Initialize connection when patientId changes
  useEffect(() => {
    if (!patientId) {
      setError("Patient ID is required");
      return;
    }

    // Check if user is authenticated via authClient session
    if (!session?.user) {
      setError("Authentication required. Please log in first.");
      setConnectionState("error");
      return;
    }

    connectToStream();

    // Cleanup on unmount or patientId change
    return () => {
      disconnect();
    };
  }, [patientId, session?.user?.id]); // Remove connectToStream and disconnect from dependencies

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (wsRef.current) {
        wsRef.current.close(1000);
        wsRef.current = null;
      }
    };
  }, []); // No dependencies for cleanup

  return {
    signal,
    classifications,
    isConnected,
    error,
    connectionState,
    reconnectAttempts,
    sessionId,
  };
}
