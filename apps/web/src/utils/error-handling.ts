import type { AuthError } from "@/types/auth";

export function getErrorMessage(error: unknown): string {
  if (error && typeof error === 'object' && 'error' in error) {
    const authError = (error as { error: AuthError }).error;
    return authError.message || authError.statusText || 'An error occurred';
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
}

export function isAuthError(error: unknown): error is { error: AuthError } {
  return error !== null && 
         typeof error === 'object' && 
         'error' in error && 
         typeof (error as any).error === 'object';
}