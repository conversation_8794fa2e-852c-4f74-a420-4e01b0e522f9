"use client";

import { useEffect, useRef, useState, useC<PERSON>back, useMemo } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData,
} from "chart.js";
import { Line } from "react-chartjs-2";
import { useECGStream } from "@/hooks/useECGStream";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { ECGChartProps, ECGDataPoint } from "@/types/ecg";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

/**
 * Simple Chart.js-based ECG Chart Component
 *
 * Clean, reliable ECG visualization using Chart.js for live streaming data.
 * Based on: https://medium.com/@pavanbhute/live-streaming-data-in-react-using-node-js-websocket-and-chart-js-3ea4d4ceac8f
 *
 * Features:
 * - Real-time ECG data streaming
 * - No licensing issues
 * - Smooth performance
 * - Medical-grade precision
 * - Better Auth integration
 */
export default function SimpleECGChart({
  patientId,
  height = 400,
  showControls = true,
  className = "",
}: ECGChartProps) {
  // Chart reference
  const chartRef = useRef<ChartJS<"line", number[], string>>(null);

  // Component state
  const [isRecording, setIsRecording] = useState(false);
  const [chartData, setChartData] = useState<ChartData<"line">>({
    labels: [],
    datasets: [
      {
        label: "ECG Signal",
        data: [],
        borderColor: "rgb(34, 197, 94)", // Green color for ECG
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.1,
        pointRadius: 0, // Hide points for smoother line
        pointHoverRadius: 0,
      },
    ],
  });

  // Chart configuration with optimized defaults for medical ECG
  const [config, setConfig] = useState({
    timeWindow: 10, // seconds
    maxDataPoints: 3600, // 10 seconds * 360 Hz (medical standard)
    scale: 1.0,
    autoScroll: true,
    // Performance optimization settings
    updateThreshold: 10, // Minimum new points before update
    memoryLimit: 10000, // Maximum points to keep in memory
  });

  // Get ECG stream data with authentication
  const { signal, isConnected, error, connectionState } =
    useECGStream(patientId);

  // Throttle updates to prevent excessive re-renders (max 30 FPS for smooth medical visualization)
  const lastUpdateTime = useRef<number>(0);
  const UPDATE_INTERVAL = 1000 / 30; // 30 FPS

  // Chart.js options optimized for real-time ECG data (memoized for performance)
  const chartOptions: ChartOptions<"line"> = useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 0, // Disable animations for real-time performance
      },
      interaction: {
        intersect: false,
        mode: "index",
      },
      plugins: {
        legend: {
          display: false, // Hide legend for cleaner look
        },
        title: {
          display: true,
          text: "Real-time ECG Signal",
          color: "#111827", // Darker gray for better contrast
          font: {
            size: 16,
            weight: "bold",
          },
        },
        tooltip: {
          enabled: false, // Disable tooltips for performance
        },
      },
      scales: {
        x: {
          type: "linear",
          position: "bottom",
          title: {
            display: true,
            text: "Time (seconds)",
            color: "#6B7280",
          },
          grid: {
            color: "rgba(107, 114, 128, 0.1)",
            drawOnChartArea: true,
          },
          ticks: {
            color: "#6B7280",
            maxTicksLimit: 10,
            callback: function (value) {
              // Format time display for better readability
              return typeof value === "number" ? value.toFixed(1) + "s" : value;
            },
          },
          // Optimize for real-time updates
          bounds: "data",
          adapters: {
            date: {},
          },
        },
        y: {
          title: {
            display: true,
            text: "Amplitude (mV)",
            color: "#6B7280",
          },
          grid: {
            color: "rgba(107, 114, 128, 0.1)",
            drawOnChartArea: true,
          },
          ticks: {
            color: "#6B7280",
            stepSize: 0.5, // Better ECG amplitude steps
            callback: function (value) {
              return typeof value === "number"
                ? value.toFixed(1) + " mV"
                : value;
            },
          },
          min: -2 * config.scale,
          max: 2 * config.scale,
          // Optimize for medical data
          beginAtZero: false,
        },
      },
    }),
    [config.scale]
  ); // Only re-create when scale changes

  /**
   * Process new ECG data points for Chart.js
   * Optimized for real-time performance with incremental updates and throttling
   */
  const processNewData = useCallback(() => {
    if (signal.length === 0) return;

    // Throttle updates to prevent excessive re-renders
    const now = Date.now();
    if (now - lastUpdateTime.current < UPDATE_INTERVAL) {
      return;
    }
    lastUpdateTime.current = now;

    // 🆕 NEW: Filter data for current patient only
    const patientSignal = signal.filter(point => point.patient_id === patientId);

    if (patientSignal.length === 0) {
      console.log(`No signal data for patient ${patientId}`);
      return;
    }

    // Get the latest data points for current patient
    const latestPoints = patientSignal.slice(-config.maxDataPoints);

    // Only update if we have new data
    if (latestPoints.length === 0) return;

    // Convert ECG data to Chart.js format with optimized processing
    const labels: number[] = []; // Use numbers for better performance
    const data: number[] = [];

    latestPoints.forEach((point: ECGDataPoint) => {
      // Use timestamp directly as number for better performance
      const timeInSeconds = point.timestamp / 1000;
      labels.push(timeInSeconds);
      data.push(point.value * config.scale);
    });

    // Update chart data with optimized dataset structure
    setChartData((prevData) => ({
      labels,
      datasets: [
        {
          ...prevData.datasets[0], // Preserve existing dataset properties
          data,
          borderColor: "rgb(34, 197, 94)",
          backgroundColor: "rgba(34, 197, 94, 0.1)",
          borderWidth: 2,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 0,
        },
      ],
    }));

    // Auto-scroll to latest data if enabled
    if (config.autoScroll && chartRef.current && latestPoints.length > 0) {
      const chart = chartRef.current;
      const latestTime = latestPoints[latestPoints.length - 1].timestamp / 1000;
      const startTime = Math.max(0, latestTime - config.timeWindow);

      // Update x-axis range for auto-scroll with bounds checking
      if (
        chart.options.scales?.x &&
        typeof chart.options.scales.x === "object"
      ) {
        chart.options.scales.x.min = startTime;
        chart.options.scales.x.max = latestTime;
        chart.update("none"); // Update without animation for performance
      }
    }
  }, [signal, patientId, config]); // 🆕 Added patientId dependency

  // Patient switch detection and state reset
  useEffect(() => {
    console.log(`🔄 Patient switch detected: ${patientId}`);

    // Reset all chart state when patient changes
    setChartData({
      labels: [],
      datasets: [{
        label: `ECG Signal - Patient ${patientId}`,
        data: [],
        borderColor: "rgb(34, 197, 94)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.1,
        pointRadius: 0,
        pointHoverRadius: 0,
      }],
    });

    // Reset recording state
    setIsRecording(false);

    // Reset config to defaults
    setConfig(prev => ({
      ...prev,
      timeWindow: 10,
      maxDataPoints: 3600,
      scale: 1.0,
    }));

    console.log(`✅ Chart state reset for patient ${patientId}`);
  }, [patientId]); // This dependency is the key!

  // Process new data when signal updates with error handling
  useEffect(() => {
    try {
      processNewData();
    } catch (error) {
      console.error("❌ Error processing ECG data:", error);
      // Don't crash the component, just log the error
    }
  }, [processNewData]);

  // Update y-axis scale when scale config changes
  useEffect(() => {
    if (chartRef.current && chartRef.current.options.scales?.y) {
      chartRef.current.options.scales.y.min = -2 * config.scale;
      chartRef.current.options.scales.y.max = 2 * config.scale;
      chartRef.current.update("none");
    }
  }, [config.scale]);

  /**
   * Control handlers
   */
  const handleStartRecording = () => {
    setIsRecording(true);
    console.log("🔴 Started ECG recording");
  };

  const handleStopRecording = () => {
    setIsRecording(false);
    console.log("⏹️ Stopped ECG recording");
  };

  const handleClearChart = () => {
    setChartData({
      labels: [],
      datasets: [
        {
          label: "ECG Signal",
          data: [],
          borderColor: "rgb(34, 197, 94)",
          backgroundColor: "rgba(34, 197, 94, 0.1)",
          borderWidth: 2,
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 0,
        },
      ],
    });
    console.log("🧹 Cleared ECG chart");
  };

  const handleScaleChange = (newScale: number) => {
    setConfig((prev) => ({ ...prev, scale: newScale }));
  };

  const handleTimeWindowChange = (newWindow: number) => {
    setConfig((prev) => ({
      ...prev,
      timeWindow: newWindow,
      maxDataPoints: newWindow * 360, // Assuming 360 Hz sample rate
    }));
  };

  // Connection status styling
  const getConnectionBadge = () => {
    switch (connectionState) {
      case "connected":
        return <Badge className="bg-green-500">Connected</Badge>;
      case "connecting":
        return <Badge className="bg-yellow-500">Connecting...</Badge>;
      case "reconnecting":
        return <Badge className="bg-orange-500">Reconnecting...</Badge>;
      case "error":
        return <Badge className="bg-red-500">Error</Badge>;
      default:
        return <Badge className="bg-gray-500">Disconnected</Badge>;
    }
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-gray-900">Simple ECG Monitor</CardTitle>
            {getConnectionBadge()}
            {isRecording && (
              <Badge className="bg-red-500 animate-pulse">Recording</Badge>
            )}
            <Badge className="bg-blue-500">Chart.js</Badge>
          </div>

          <div className="flex items-center gap-2 text-sm text-gray-800">
            <span>Patient: {patientId}</span>
            <span>•</span>
            <span>Points: {signal.length}</span>
            <span>•</span>
            <span>Window: {config.timeWindow}s</span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Chart Container */}
        <div style={{ height: `${height}px` }} className="w-full">
          <Line ref={chartRef} data={chartData} options={chartOptions} />
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">⚠️ {error}</p>
          </div>
        )}

        {/* Controls */}
        {showControls && (
          <div className="flex flex-wrap items-center gap-3 pt-3 border-t">
            <div className="flex gap-2">
              {!isRecording ? (
                <Button
                  onClick={handleStartRecording}
                  disabled={!isConnected}
                  className="bg-red-500 hover:bg-red-600"
                >
                  🔴 Start Recording
                </Button>
              ) : (
                <Button
                  onClick={handleStopRecording}
                  className="bg-gray-500 hover:bg-gray-600"
                >
                  ⏹️ Stop Recording
                </Button>
              )}

              <Button onClick={handleClearChart} variant="outline">
                🧹 Clear
              </Button>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-800">Scale:</label>
                <select
                  value={config.scale}
                  onChange={(e) => handleScaleChange(Number(e.target.value))}
                  className="px-2 py-1 border rounded text-sm text-gray-800"
                >
                  <option value={0.5}>0.5x</option>
                  <option value={1}>1x</option>
                  <option value={2}>2x</option>
                  <option value={5}>5x</option>
                </select>
              </div>

              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-800">Window:</label>
                <select
                  value={config.timeWindow}
                  onChange={(e) =>
                    handleTimeWindowChange(Number(e.target.value))
                  }
                  className="px-2 py-1 border rounded text-sm text-gray-800"
                >
                  <option value={5}>5s</option>
                  <option value={10}>10s</option>
                  <option value={30}>30s</option>
                  <option value={60}>60s</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
