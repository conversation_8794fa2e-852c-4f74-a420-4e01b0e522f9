import { authClient } from "@/lib/auth-client";
import Loader from "./loader";
import { CustomButton } from "./ui/custom-button";
import { FormField } from "./ui/form-field";
import { useSignIn } from "@/hooks/useSignIn";

export default function SignInForm() {
  const { isPending } = authClient.useSession();
  const { form, handleGoogleSignIn, handleForgotPassword } = useSignIn();

  if (isPending) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen lg:min-h-0 w-full px-6 lg:px-0 py-8 lg:py-0">
      <div className="flex flex-col gap-8 lg:gap-12 items-center justify-center w-full max-w-[462px]">
        {/* Header Section */}
        <div className="flex flex-col gap-5 lg:gap-7 items-center justify-center text-center w-full">
          <div className="flex flex-col justify-center w-full">
            <h1 className="font-inter text-[28px] lg:text-[36px] font-semibold text-[#0c1421] tracking-[0.36px] leading-none">
              Welcome Back <span className="font-normal">👋</span>
            </h1>
          </div>
          <div className="flex flex-col justify-center w-full">
            <p className="font-inter font-normal text-[18px] lg:text-[20px] text-[#313957] tracking-[0.2px] leading-[1.6]">
              Log in to manage and monitor your patients with ease.
            </p>
          </div>
        </div>

        {/* Form Section */}
        <div className="flex flex-col gap-6 items-end justify-center w-full">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              e.stopPropagation();
              form.handleSubmit();
            }}
            className="flex flex-col gap-6 w-full"
            aria-label="Sign in form"
          >
            {/* Email Field */}
            <form.Field name="email">
              {(field) => (
                <FormField
                  field={field}
                  label="Email"
                  type="email"
                  placeholder="<EMAIL>"
                />
              )}
            </form.Field>

            {/* Password Field */}
            <form.Field name="password">
              {(field) => (
                <FormField
                  field={field}
                  label="Password"
                  type="password"
                  placeholder="At least 8 characters"
                />
              )}
            </form.Field>

            {/* Forgot Password Link */}
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="font-roboto font-normal text-[16px] text-[#1e4ae9] tracking-[0.16px] hover:underline focus:outline-none focus:ring-2 focus:ring-[#1e4ae9] rounded"
                aria-label="Forgot password? Reset your password"
              >
                Forgot Password?
              </button>
            </div>

            {/* Sign In Button */}
            <form.Subscribe>
              {(state) => (
                <CustomButton
                  type="submit"
                  disabled={!state.canSubmit || state.isSubmitting}
                >
                  {state.isSubmitting ? "Submitting..." : "Sign in"}
                </CustomButton>
              )}
            </form.Subscribe>
          </form>
        </div>

        {/* Social Sign In Section */}
        <div className="flex flex-col gap-6 items-start justify-start w-full">
          {/* Divider */}
          <div className="flex gap-4 items-center justify-center px-0 py-2.5 w-full">
            <div className="flex-1 h-px bg-[#cfdfe2]" />
            <div className="font-roboto font-normal text-[16px] text-[#294957] tracking-[0.16px] text-center">
              Or
            </div>
            <div className="flex-1 h-px bg-[#cfdfe2]" />
          </div>

          {/* Google Sign In Button */}
          <div className="flex flex-col gap-4 w-full">
            <button
              type="button"
              onClick={handleGoogleSignIn}
              className="bg-[#f3f9fa] flex gap-4 items-center justify-center px-[9px] py-3 rounded-xl w-full hover:bg-[#e8f4f6] transition-colors focus:outline-none focus:ring-2 focus:ring-[#e7542a]"
              aria-label="Sign in with Google"
            >
              <div className="w-7 h-7">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M27.44 14.32c0-.96-.08-1.92-.24-2.84H14v5.36h7.56c-.32 1.72-1.32 3.2-2.8 4.16v3.44h4.52c2.64-2.44 4.16-6.04 4.16-10.12z"
                    fill="#4285F4"
                  />
                  <path
                    d="M14 28c3.76 0 6.92-1.24 9.24-3.36l-4.52-3.44c-1.24.84-2.84 1.32-4.72 1.32-3.64 0-6.72-2.44-7.84-5.76H1.56v3.56C3.84 24.84 8.6 28 14 28z"
                    fill="#34A853"
                  />
                  <path
                    d="M6.16 16.76c-.28-.84-.44-1.72-.44-2.64s.16-1.8.44-2.64V7.92H1.56C.56 9.92 0 11.92 0 14.12s.56 4.2 1.56 6.2l4.6-3.56z"
                    fill="#FBBC04"
                  />
                  <path
                    d="M14 5.52c2.04 0 3.88.72 5.32 2.12l4-4C20.92 1.44 17.76 0 14 0 8.6 0 3.84 3.16 1.56 7.92l4.6 3.56C7.28 7.96 10.36 5.52 14 5.52z"
                    fill="#EA4335"
                  />
                </svg>
              </div>
              <div className="font-roboto font-normal text-[16px] text-[#313957] tracking-[0.16px]">
                Sign in with Google
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="mt-8 lg:mt-12 font-roboto font-normal text-[16px] text-[#8897ad] text-center tracking-[0.16px]">
        © 2025 ALL RIGHTS RESERVED
      </div>
    </div>
  );
}
