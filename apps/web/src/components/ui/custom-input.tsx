import * as React from "react";
import { cn } from "@/lib/utils";

export interface CustomInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** The label text to display above the input */
  label?: string;
  /** Error message to display below the input */
  error?: string;
  /** Size variant of the input */
  size?: 'sm' | 'md' | 'lg';
  /** Whether the input is in a loading state */
  loading?: boolean;
}

const CustomInput = React.forwardRef<HTMLInputElement, CustomInputProps>(
  ({ className, type, label, placeholder, error, id, size, loading, ...props }, ref) => {
    const generatedId = React.useId();
    const inputId = id || generatedId;
    const errorId = error ? `${inputId}-error` : undefined;

    return (
      <div className="flex flex-col gap-2 w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="font-roboto font-normal text-[16px] text-[#0c1421] tracking-[0.16px]"
          >
            {label}
          </label>
        )}
        <div className="relative w-full">
          <input
            id={inputId}
            type={type}
            aria-describedby={errorId}
            aria-invalid={error ? true : undefined}
            className={cn(
              "w-full h-12 bg-[#f7fbff] border border-[#d4d7e3] rounded-xl px-4 py-3",
              "font-roboto font-normal text-[16px] tracking-[0.16px]",
              "placeholder:text-[#8897ad] placeholder:font-roboto placeholder:font-normal",
              "focus:outline-none focus:ring-2 focus:ring-[#e7542a] focus:border-transparent",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              // Override autofill styling to maintain consistent background and text color
              "[&:-webkit-autofill]:shadow-[0_0_0_30px_#f7fbff_inset] [&:-webkit-autofill]:[-webkit-text-fill-color:#0c1421]",
              "[&:-webkit-autofill:hover]:shadow-[0_0_0_30px_#f7fbff_inset] [&:-webkit-autofill:hover]:[-webkit-text-fill-color:#0c1421]",
              "[&:-webkit-autofill:focus]:shadow-[0_0_0_30px_#f7fbff_inset] [&:-webkit-autofill:focus]:[-webkit-text-fill-color:#0c1421]",
              error && "border-red-500 focus:ring-red-500",
              className
            )}
            placeholder={placeholder}
            ref={ref}
            {...props}
          />
        </div>
        {error && (
          <p id={errorId} className="text-red-500 text-sm" role="alert">
            {error}
          </p>
        )}
      </div>
    );
  }
);
CustomInput.displayName = "CustomInput";

export { CustomInput };
