import { CustomInput } from "./custom-input";

interface FormFieldProps {
  /** The form field object from TanStack Form */
  field: {
    name: string;
    state: {
      value: string;
      meta: {
        errors: Array<any>;
      };
    };
    handleBlur: () => void;
    handleChange: (value: string) => void;
  };
  /** The label text for the input */
  label: string;
  /** The input type (text, email, password, etc.) */
  type?: string;
  /** Placeholder text for the input */
  placeholder?: string;
}

export function FormField({ field, label, type = "text", placeholder }: FormFieldProps) {
  return (
    <div className="flex flex-col gap-2 w-full">
      <CustomInput
        id={field.name}
        name={field.name}
        type={type}
        label={label}
        placeholder={placeholder}
        value={field.state.value}
        onBlur={field.handleBlur}
        onChange={(e) => field.handleChange(e.target.value)}
      />
      {field.state.meta.errors.map((error) => (
        <p key={error?.message} className="text-red-500 text-sm">
          {error?.message}
        </p>
      ))}
    </div>
  );
}