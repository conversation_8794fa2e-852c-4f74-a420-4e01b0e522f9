export function FormSkeleton() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen lg:min-h-0 w-full px-6 lg:px-0 py-8 lg:py-0">
      <div className="flex flex-col gap-8 lg:gap-12 items-center justify-center w-full max-w-[462px]">
        {/* Header Section Skeleton */}
        <div className="flex flex-col gap-5 lg:gap-7 items-center justify-center text-center w-full">
          <div className="flex flex-col justify-center w-full">
            <div className="h-9 lg:h-11 bg-gray-200 rounded w-80 animate-pulse mx-auto" />
          </div>
          <div className="flex flex-col justify-center w-full">
            <div className="h-6 lg:h-7 bg-gray-200 rounded w-96 animate-pulse mx-auto" />
          </div>
        </div>

        {/* Form Section Skeleton */}
        <div className="flex flex-col gap-6 items-end justify-center w-full">
          <div className="flex flex-col gap-6 w-full">
            {/* Email Field Skeleton */}
            <div className="flex flex-col gap-2 w-full">
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse" />
              <div className="h-12 bg-gray-200 rounded-xl animate-pulse" />
            </div>

            {/* Password Field Skeleton */}
            <div className="flex flex-col gap-2 w-full">
              <div className="h-4 bg-gray-200 rounded w-20 animate-pulse" />
              <div className="h-12 bg-gray-200 rounded-xl animate-pulse" />
            </div>

            {/* Forgot Password Link Skeleton */}
            <div className="flex justify-end">
              <div className="h-4 bg-gray-200 rounded w-32 animate-pulse" />
            </div>

            {/* Sign In Button Skeleton */}
            <div className="h-12 bg-gray-200 rounded-xl animate-pulse" />
          </div>
        </div>

        {/* Social Sign In Section Skeleton */}
        <div className="flex flex-col gap-6 items-start justify-start w-full">
          {/* Divider Skeleton */}
          <div className="flex gap-4 items-center justify-center px-0 py-2.5 w-full">
            <div className="flex-1 h-px bg-gray-200" />
            <div className="h-4 bg-gray-200 rounded w-6 animate-pulse" />
            <div className="flex-1 h-px bg-gray-200" />
          </div>

          {/* Google Sign In Button Skeleton */}
          <div className="flex flex-col gap-4 w-full">
            <div className="h-12 bg-gray-200 rounded-xl animate-pulse" />
          </div>
        </div>
      </div>

      {/* Footer Skeleton */}
      <div className="mt-8 lg:mt-12">
        <div className="h-4 bg-gray-200 rounded w-48 animate-pulse mx-auto" />
      </div>
    </div>
  );
}

export function InputSkeleton() {
  return (
    <div className="flex flex-col gap-2 w-full">
      <div className="h-4 bg-gray-200 rounded w-16 animate-pulse" />
      <div className="h-12 bg-gray-200 rounded-xl animate-pulse" />
    </div>
  );
}

export function ButtonSkeleton() {
  return <div className="h-12 bg-gray-200 rounded-xl animate-pulse w-full" />;
}