export const COLORS = {
  primary: '#e7542a',
  primaryHover: '#d14420',
  primaryActive: '#bf3e1d',
  text: {
    primary: '#0c1421',
    secondary: '#313957',
    muted: '#8897ad',
    accent: '#1e4ae9',
  },
  border: '#d4d7e3',
  background: {
    primary: '#f7fbff',
    secondary: '#f3f9fa',
    hover: '#e8f4f6',
  },
  divider: '#cfdfe2',
  success: '#22c55e',
  error: '#ef4444',
  warning: '#f59e0b',
} as const;

export const FONT_SIZES = {
  xs: '12px',
  sm: '14px',
  base: '16px',
  lg: '18px',
  xl: '20px',
  '2xl': '24px',
  '3xl': '28px',
  '4xl': '36px',
  '5xl': '64px',
} as const;

export const FONT_WEIGHTS = {
  normal: 400,
  medium: 500,
  semibold: 600,
  bold: 700,
} as const;

export const SPACING = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px',
  '3xl': '64px',
} as const;

export const BORDER_RADIUS = {
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  '2xl': '24px',
  '3xl': '30px',
  full: '9999px',
} as const;

export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

export const FONT_FAMILIES = {
  inter: 'Inter',
  roboto: 'Roboto',
  inria: 'Inria_Serif',
} as const;

export const TRANSITIONS = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
} as const;