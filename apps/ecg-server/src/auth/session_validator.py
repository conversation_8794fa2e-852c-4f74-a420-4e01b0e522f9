"""
Session validation for Better Auth integration.

This module provides session validation by communicating with the
Better Auth server running in the Next.js application.
"""

import os
import logging
from typing import Optional

import httpx
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class UserSession(BaseModel):
    """User session information from Better Auth."""
    
    user_id: str = Field(..., description="Unique user identifier")
    role: str = Field(..., description="User role (doctor, patient, admin)")
    email: str = Field(..., description="User email address")
    name: Optional[str] = Field(None, description="User display name")
    account_status: Optional[str] = Field(None, description="Account status")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            # Add any custom JSON encoders if needed
        }


class SessionValidator:
    """
    Validates user sessions with the Better Auth server.
    
    This class handles authentication by validating session tokens
    with the Better Auth server and extracting user information.
    """
    
    def __init__(self, auth_server_url: Optional[str] = None) -> None:
        """
        Initialize the session validator.
        
        Args:
            auth_server_url: URL of the Better Auth server. If None, 
                           uses AUTH_SERVER_URL environment variable.
        """
        self.auth_server_url = auth_server_url or os.getenv("AUTH_SERVER_URL", "http://localhost:3000")
        self.timeout = 10.0  # HTTP request timeout in seconds
        
        logger.info(f"🔐 SessionValidator initialized with auth server: {self.auth_server_url}")

    async def _get_development_doctor_id(self) -> str:
        """
        Get a development doctor user ID from the database.

        Returns:
            User ID of a doctor for development mode
        """
        try:
            # Use the shared database manager instance
            from ..main import db_manager

            # Get the first doctor user from the database
            query = """
                SELECT u.id
                FROM "user" u
                JOIN doctors d ON u.id = d.user_id
                WHERE u.role = 'doctor'
                AND u.account_status = 'active'
                AND u.email = '<EMAIL>'
                LIMIT 1
            """

            result = await db_manager._execute_fetchrow(query)
            if result:
                return result['id']
            else:
                # Fallback: return a default doctor ID if none found
                logger.warning("⚠️  No doctor found in database, using fallback ID")
                return "dev-doctor-fallback-id"

        except Exception as e:
            logger.error(f"❌ Error getting development doctor ID: {e}")
            return "dev-doctor-fallback-id"

    async def _try_better_auth_validation(self, session_token: str) -> Optional[dict]:
        """
        Try to validate session token with Better Auth API.

        Args:
            session_token: The session token to validate

        Returns:
            Session data dictionary or None if invalid
        """
        try:
            async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as client:
                # URL decode the session token if needed
                import urllib.parse
                decoded_token = urllib.parse.unquote(session_token)

                # Set the session cookie in the request headers (correct format)
                headers = {
                    "Content-Type": "application/json",
                    "Cookie": f"better-auth.session_token={decoded_token}"
                }

                logger.debug(f"🔐 Trying Better Auth validation with token: {decoded_token[:20]}...")

                # Use GET method as the endpoint supports both GET and POST
                response = await client.get(
                    f"{self.auth_server_url}/api/auth/validate-session",
                    headers=headers
                )
                logger.debug(f"🔐 Better Auth validation response: {response.status_code}")

                if response.status_code == 200:
                    session_data = response.json()
                    if session_data and session_data.get('user'):
                        logger.info(f"✅ Session validated via Better Auth API for user: {session_data['user'].get('email', 'unknown')}")
                        return session_data
                    else:
                        logger.debug(f"❌ Better Auth returned empty session data")
                        return None
                else:
                    logger.debug(f"❌ Better Auth validation failed: {response.status_code}")
                    if response.status_code == 401:
                        logger.debug(f"❌ Session token appears to be invalid or expired")
                    return None
        except Exception as e:
            logger.debug(f"❌ Better Auth API validation failed: {e}")
            return None

    async def validate_session(self, session_token: str) -> Optional[UserSession]:
        """
        Validate a session token with optimized authentication flow.

        Args:
            session_token: The session token to validate (can be actual token or user ID)

        Returns:
            UserSession object if valid, None if invalid

        Raises:
            httpx.HTTPError: If there's an HTTP communication error
        """
        if not session_token:
            logger.warning("❌ Empty session token provided")
            return None

        try:
            session_data = None

            # In development mode, try Better Auth first, then fallback if needed
            if os.getenv("DEBUG", "false").lower() == "true":
                logger.debug(f"🔧 Development mode: Trying Better Auth first")

                # Try Better Auth API first (most reliable for session tokens)
                session_data = await self._try_better_auth_validation(session_token)

                # If Better Auth fails, use development fallback
                if not session_data:
                    logger.debug(f"🔧 Better Auth failed, using development fallback")
                    if session_token and len(session_token) > 10:
                        # Use a real doctor user ID from the seeded database
                        dev_doctor_user_id = await self._get_development_doctor_id()

                        session_data = {
                            "user": {
                                "id": dev_doctor_user_id,
                                "email": "<EMAIL>",
                                "name": "Dr. Emily Rodriguez (Dev Mode)",
                                "role": "doctor",
                                "accountStatus": "active"
                            },
                            "session": {
                                "id": "dev-session-id",
                                "token": session_token
                            }
                        }
                        logger.info(f"✅ Development fallback session validated for user: <EMAIL>")
                else:
                    logger.info(f"✅ Better Auth session validated in development mode")

            # Production authentication flow (only if not already validated in dev mode)
            if not session_data:
                # Try Better Auth API first (most reliable for session tokens)
                session_data = await self._try_better_auth_validation(session_token)

                # Fallback to user ID validation (for direct user ID tokens)
                if not session_data:
                    logger.debug(f"🔍 Better Auth failed, trying user ID validation...")
                    session_data = await self._validate_user_id_token(session_token)

                # Final fallback to direct session token lookup
                if not session_data:
                    logger.debug(f"🔍 User ID failed, trying direct session lookup...")
                    session_data = await self._validate_session_from_database(session_token)

            if not session_data:
                logger.error(f"❌ All validation methods failed for session token")
                return None

            # Process the valid session data we found
            try:
                # Extract user information from Better Auth response
                user_data = session_data.get('user', {})
                session_info = session_data.get('session', {})

                if not user_data:
                    logger.warning("⚠️  No user data in session response")
                    return None

                logger.info(f"✅ Valid session for user: {user_data.get('email', 'unknown')}")
                logger.debug(f"👤 User role: {user_data.get('role', 'unknown')}")

                return UserSession(
                    user_id=user_data.get('id', ''),
                    role=user_data.get('role', 'patient'),
                    email=user_data.get('email', ''),
                    name=user_data.get('name'),
                    account_status=user_data.get('accountStatus')
                )
            except Exception as parse_error:
                logger.error(f"❌ Failed to parse session data: {parse_error}")
                return None

        except Exception as e:
            logger.error(f"❌ Unexpected error during session validation: {e}")
            return None
            
        except Exception as e:
            logger.error(f"💥 Unexpected error during session validation: {e}")
            return None
    
    async def validate_doctor_role(self, session_token: str) -> Optional[UserSession]:
        """
        Validate session and ensure user has doctor or admin role.
        
        Args:
            session_token: The session token to validate
            
        Returns:
            UserSession object if valid doctor/admin, None otherwise
        """
        session = await self.validate_session(session_token)
        
        if session and session.role in ["doctor", "admin"]:
            logger.debug(f"✅ Valid doctor/admin session: {session.email}")
            return session
        
        if session:
            logger.warning(f"🚫 Insufficient permissions for user: {session.email} (role: {session.role})")
        
        return None
    
    async def health_check(self) -> bool:
        """
        Check if the auth server is reachable by testing the root endpoint.

        Returns:
            True if auth server is reachable, False otherwise
        """
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                # Test the root endpoint instead of /health since Next.js doesn't have /health
                response = await client.get(f"{self.auth_server_url}/")
                # Accept any successful response (200, 404 for pages, etc.)
                return response.status_code < 500
        except Exception as e:
            logger.error(f"❌ Auth server health check failed: {e}")
            return False
    
    async def _validate_user_id_token(self, user_id: str) -> Optional[dict]:
        """
        Validate a user ID token by checking if it corresponds to an active user.
        This is used when the web app sends user IDs instead of session cookies.

        Args:
            user_id: The user ID to validate

        Returns:
            Session data dictionary or None if invalid
        """
        try:
            # Use the shared database manager instance to avoid creating new pools
            from ..main import db_manager

            # Query the user table to check if user exists and is active
            # Fixed: Use $1 placeholder for asyncpg instead of %s
            query = """
                SELECT
                    u.id as user_id,
                    u.name,
                    u.email,
                    u.role,
                    u.account_status,
                    u.email_verified
                FROM "user" u
                WHERE u.id = $1
                    AND u.account_status = 'active'
                    AND u.email_verified = true
            """

            result = await db_manager._execute_fetchrow(query, user_id)

            if result:
                logger.info(f"✅ Valid user ID token for: {result['email']}")
                return {
                    "user": {
                        "id": result["user_id"],
                        "name": result["name"],
                        "email": result["email"],
                        "role": result["role"],
                        "accountStatus": result["account_status"]
                    },
                    "session": {
                        "id": f"user-id-session-{user_id}",
                        "userId": result["user_id"],
                        "token": user_id
                    }
                }
            else:
                logger.warning(f"❌ Invalid or inactive user ID: {user_id}")
                return None

        except Exception as e:
            logger.error(f"❌ Database validation error for user ID {user_id}: {e}")
            return None

    async def _validate_session_from_database(self, session_token: str) -> Optional[dict]:
        """
        Validate session token directly from database (production method).
        
        Args:
            session_token: The session token to validate
            
        Returns:
            Session data dictionary or None if invalid
        """
        try:
            # Use the shared database manager instance to avoid creating new pools
            from ..main import db_manager
            
            # Query the session and user tables directly
            query = """
                SELECT 
                    s.id as session_id,
                    s.token as session_token,
                    s.expires_at,
                    s.user_id,
                    u.id as user_id,
                    u.name,
                    u.email,
                    u.role,
                    u.account_status
                FROM session s
                JOIN "user" u ON s.user_id = u.id
                WHERE s.token = $1 
                AND s.expires_at > NOW()
                AND u.account_status = 'active'
            """
            
            row = await db_manager._execute_fetchrow(query, session_token)
            
            if not row:
                logger.debug(f"🚫 Invalid or expired session token in database")
                return None
            
            # Convert database row to session data format
            session_data = {
                "user": {
                    "id": row["user_id"],
                    "name": row["name"],
                    "email": row["email"],
                    "role": row["role"],
                    "accountStatus": row["account_status"]
                },
                "session": {
                    "id": row["session_id"],
                    "token": row["session_token"],
                    "userId": row["user_id"]
                }
            }
            
            logger.info(f"✅ Session validated from database: {row['email']}")
            return session_data
            
        except Exception as e:
            logger.error(f"❌ Database session validation failed: {e}")
            return None