"""
ECG Data Simulator for HealthLink

This module provides ECG signal simulation for testing and development.
In production, this would be replaced with real ECG device integration.
"""

import asyncio
import numpy as np
import logging
from typing import AsyncGenerator, Optional, Dict, Any

logger = logging.getLogger(__name__)


class ECGSimulator:
    """
    Simulates ECG data streaming for testing purposes.

    Generates realistic ECG waveforms based on mathematical models
    or loads sample data from ECG databases.
    """

    def __init__(self, sample_rate: int = 360, db_manager=None):
        """
        Initialize the ECG simulator.

        Args:
            sample_rate: ECG sampling rate in Hz (default: 360 Hz)
            db_manager: Database manager for patient information lookup
        """
        self.sample_rate = sample_rate
        self.interval = 1.0 / sample_rate
        self.db_manager = db_manager

        logger.info(f"🫀 ECGSimulator initialized (sample rate: {sample_rate} Hz)")
    
    async def stream_patient_ecg(
        self, 
        patient_id: str, 
        duration_seconds: Optional[int] = None
    ) -> AsyncGenerator[float, None]:
        """
        Stream ECG data for a specific patient.
        
        Args:
            patient_id: Patient identifier
            duration_seconds: Duration to stream (None for infinite)
            
        Yields:
            ECG sample values as floats
        """
        logger.info(f"🔄 Starting ECG stream for patient {patient_id}")

        # Load patient-specific ECG data
        signal = await self._load_patient_data(patient_id)
        
        sample_count = 0
        max_samples = duration_seconds * self.sample_rate if duration_seconds else None

        # Use precise timing to avoid drift
        start_time = asyncio.get_event_loop().time()

        try:
            while True:
                if max_samples and sample_count >= max_samples:
                    logger.info(f"✅ ECG stream completed for patient {patient_id} ({duration_seconds}s)")
                    break

                # Cycle through the signal data
                sample_index = sample_count % len(signal)
                sample_value = float(signal[sample_index])

                yield sample_value

                sample_count += 1

                # Calculate precise next sample time to avoid timing drift
                next_sample_time = start_time + (sample_count * self.interval)
                current_time = asyncio.get_event_loop().time()
                sleep_duration = max(0, next_sample_time - current_time)

                if sleep_duration > 0:
                    await asyncio.sleep(sleep_duration)
                
        except asyncio.CancelledError:
            logger.info(f"🛑 ECG stream cancelled for patient {patient_id}")
            raise
        except Exception as e:
            logger.error(f"❌ ECG stream error for patient {patient_id}: {e}")
            raise
    
    async def _load_patient_data(self, patient_id: str) -> np.ndarray:
        """
        Load ECG data for a specific patient.

        Args:
            patient_id: Patient identifier

        Returns:
            ECG signal as numpy array
        """
        try:
            # Generate patient-specific ECG data based on patient profile
            logger.debug(f"🔍 Loading ECG data for patient {patient_id}")

            # Get patient-specific ECG characteristics
            patient_profile = await self._get_patient_profile(patient_id)
            return self._generate_patient_specific_ecg(patient_profile)

        except Exception as e:
            logger.warning(f"⚠️  Failed to load ECG data for patient {patient_id}: {e}")
            logger.info("🔄 Falling back to normal ECG generation")
            return self._generate_patient_specific_ecg({"condition": "normal", "heart_rate": 72})
    
    async def _get_patient_profile(self, patient_id: str) -> dict:
        """
        Get patient-specific ECG profile based on patient ID.

        Args:
            patient_id: Patient identifier

        Returns:
            Dictionary with patient ECG characteristics
        """
        # Try to get patient info from database first
        patient_info = None
        if self.db_manager:
            try:
                patient_info = await self.db_manager.get_patient_info(patient_id)
            except Exception as e:
                logger.warning(f"⚠️  Could not fetch patient info from database: {e}")

        # Define cardiac condition profiles based on patient characteristics
        if patient_info:
            name = patient_info.get("name", "Unknown Patient")
            mrn = patient_info.get("medical_record_number", "")

            # Assign conditions based on MRN or patient characteristics
            if mrn == "MRN001":  # John Smith
                profile = {
                    "name": name,
                    "condition": "normal",
                    "heart_rate": 68,  # Slightly lower, athletic
                    "description": f"Healthy patient with normal sinus rhythm (MRN: {mrn})"
                }
            elif mrn == "MRN002":  # Maria Garcia
                profile = {
                    "name": name,
                    "condition": "atrial_fibrillation",
                    "heart_rate": 95,  # Irregular, faster
                    "description": f"Patient with atrial fibrillation (MRN: {mrn})"
                }
            elif mrn == "MRN003":  # Robert Johnson
                profile = {
                    "name": name,
                    "condition": "bradycardia",
                    "heart_rate": 45,  # Slow heart rate
                    "description": f"Patient with bradycardia (MRN: {mrn})"
                }
            elif mrn == "MRN-TEST-001" or patient_id == "test-patient-1":  # Test Patient
                profile = {
                    "name": name,
                    "condition": "ventricular_tachycardia",
                    "heart_rate": 180,  # Very fast, dangerous
                    "description": f"Test patient with ventricular tachycardia episodes (MRN: {mrn})"
                }
            else:
                # For any other patient, assign a condition based on their name or ID
                # This creates variety for demo purposes
                patient_hash = hash(patient_id) % 4
                if patient_hash == 0:
                    profile = {
                        "name": name,
                        "condition": "normal",
                        "heart_rate": 72,
                        "description": f"Patient with normal sinus rhythm (MRN: {mrn})"
                    }
                elif patient_hash == 1:
                    profile = {
                        "name": name,
                        "condition": "atrial_fibrillation",
                        "heart_rate": 88,
                        "description": f"Patient with atrial fibrillation (MRN: {mrn})"
                    }
                elif patient_hash == 2:
                    profile = {
                        "name": name,
                        "condition": "bradycardia",
                        "heart_rate": 52,
                        "description": f"Patient with bradycardia (MRN: {mrn})"
                    }
                else:
                    profile = {
                        "name": name,
                        "condition": "normal",
                        "heart_rate": 75,
                        "description": f"Patient with normal sinus rhythm (MRN: {mrn})"
                    }

            logger.info(f"🫀 Loading profile for {profile['name']}: {profile['description']}")
            return profile

        # Fallback profiles for when database is not available
        fallback_profiles = {
            "test-patient-1": {
                "name": "Test Patient ECG",
                "condition": "ventricular_tachycardia",
                "heart_rate": 180,
                "description": "Test patient with ventricular tachycardia episodes"
            }
        }

        # Check fallback profiles
        if patient_id in fallback_profiles:
            profile = fallback_profiles[patient_id]
            logger.info(f"🫀 Using fallback profile for {profile['name']}: {profile['description']}")
            return profile

        # Default normal profile for unknown patients
        logger.info(f"🫀 Using default normal profile for patient {patient_id}")
        return {
            "name": f"Patient {patient_id}",
            "condition": "normal",
            "heart_rate": 72,
            "description": "Default normal sinus rhythm"
        }

    def _generate_patient_specific_ecg(self, patient_profile: dict) -> np.ndarray:
        """
        Generate patient-specific ECG data based on their cardiac condition.

        Args:
            patient_profile: Dictionary with patient characteristics

        Returns:
            Patient-specific ECG signal as numpy array
        """
        condition = patient_profile.get("condition", "normal")
        base_heart_rate = patient_profile.get("heart_rate", 72)
        patient_name = patient_profile.get("name", "Unknown")

        logger.info(f"🫀 Generating {condition} ECG for {patient_name} (HR: {base_heart_rate} BPM)")

        # Generate 10 seconds of ECG data at the specified sample rate
        duration = 10.0  # seconds
        num_samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, num_samples)

        if condition == "normal":
            ecg_signal = self._generate_normal_ecg(t, base_heart_rate)
        elif condition == "atrial_fibrillation":
            ecg_signal = self._generate_afib_ecg(t, base_heart_rate)
        elif condition == "bradycardia":
            ecg_signal = self._generate_bradycardia_ecg(t, base_heart_rate)
        elif condition == "ventricular_tachycardia":
            ecg_signal = self._generate_vtach_ecg(t, base_heart_rate)
        else:
            # Fallback to normal
            ecg_signal = self._generate_normal_ecg(t, base_heart_rate)

        logger.debug(f"✅ Generated {condition} ECG: {len(ecg_signal)} samples")
        return ecg_signal

    def _generate_normal_ecg(self, t: np.ndarray, heart_rate_bpm: int) -> np.ndarray:
        """Generate normal sinus rhythm ECG."""
        heart_rate_hz = heart_rate_bpm / 60.0

        # Normal ECG components
        # P wave (atrial depolarization)
        p_wave = 0.1 * np.sin(2 * np.pi * heart_rate_hz * t + np.pi/6)

        # QRS complex (ventricular depolarization) - sharp, prominent
        qrs_complex = 0.8 * np.sin(2 * np.pi * heart_rate_hz * t) * np.exp(-((t % (1/heart_rate_hz) - 0.15)**2) / 0.01)

        # T wave (ventricular repolarization)
        t_wave = 0.2 * np.sin(2 * np.pi * heart_rate_hz * t - np.pi/3)

        # Combine components
        ecg_signal = p_wave + qrs_complex + t_wave

        # Add minimal noise for healthy heart
        noise = 0.03 * np.random.randn(len(t))
        ecg_signal += noise

        # Minimal baseline wander
        baseline_wander = 0.05 * np.sin(2 * np.pi * 0.1 * t)
        ecg_signal += baseline_wander

        return ecg_signal

    def _generate_afib_ecg(self, t: np.ndarray, heart_rate_bpm: int) -> np.ndarray:
        """Generate atrial fibrillation ECG with irregular rhythm."""
        base_heart_rate_hz = heart_rate_bpm / 60.0

        # Irregular heart rate variation (hallmark of AFib)
        rr_intervals = []
        current_time = 0
        while current_time < t[-1]:
            # Random RR interval between 0.4 and 1.2 seconds (50-150 BPM)
            rr_interval = np.random.uniform(0.4, 1.2)
            rr_intervals.append(current_time)
            current_time += rr_interval

        ecg_signal = np.zeros_like(t)

        # Generate QRS complexes at irregular intervals
        for beat_time in rr_intervals:
            if beat_time < t[-1]:
                # Find closest time index
                beat_idx = np.argmin(np.abs(t - beat_time))

                # QRS complex - narrower and more irregular in AFib
                qrs_width = 0.08  # seconds
                qrs_samples = int(qrs_width * self.sample_rate)
                start_idx = max(0, beat_idx - qrs_samples//2)
                end_idx = min(len(t), beat_idx + qrs_samples//2)

                if end_idx > start_idx:
                    qrs_t = t[start_idx:end_idx] - beat_time
                    qrs_amplitude = np.random.uniform(0.6, 1.0)  # Variable amplitude
                    qrs = qrs_amplitude * np.exp(-(qrs_t**2) / (0.01))
                    ecg_signal[start_idx:end_idx] += qrs

        # No clear P waves in AFib - replaced with fibrillatory waves
        fibrillatory_waves = 0.05 * np.random.randn(len(t)) * np.sin(2 * np.pi * 8 * t)
        ecg_signal += fibrillatory_waves

        # Irregular T waves
        t_wave = 0.15 * np.sin(2 * np.pi * base_heart_rate_hz * t - np.pi/2) * (1 + 0.3 * np.random.randn(len(t)))
        ecg_signal += t_wave

        # More noise due to irregular electrical activity
        noise = 0.08 * np.random.randn(len(t))
        ecg_signal += noise

        return ecg_signal

    def _generate_bradycardia_ecg(self, t: np.ndarray, heart_rate_bpm: int) -> np.ndarray:
        """Generate bradycardia ECG with slow heart rate."""
        heart_rate_hz = heart_rate_bpm / 60.0

        # Normal morphology but slower rate
        # P wave - more prominent due to slower rate
        p_wave = 0.12 * np.sin(2 * np.pi * heart_rate_hz * t + np.pi/6)

        # QRS complex - normal morphology
        qrs_complex = 0.9 * np.sin(2 * np.pi * heart_rate_hz * t) * np.exp(-((t % (1/heart_rate_hz) - 0.2)**2) / 0.015)

        # T wave - more pronounced
        t_wave = 0.25 * np.sin(2 * np.pi * heart_rate_hz * t - np.pi/3)

        # Combine components
        ecg_signal = p_wave + qrs_complex + t_wave

        # Minimal noise
        noise = 0.04 * np.random.randn(len(t))
        ecg_signal += noise

        # Slight baseline wander
        baseline_wander = 0.06 * np.sin(2 * np.pi * 0.08 * t)
        ecg_signal += baseline_wander

        return ecg_signal

    def _generate_vtach_ecg(self, t: np.ndarray, heart_rate_bpm: int) -> np.ndarray:
        """Generate ventricular tachycardia ECG with fast, wide QRS complexes."""
        heart_rate_hz = heart_rate_bpm / 60.0

        # Wide QRS complexes (>120ms) - characteristic of VTach
        # No clear P waves
        # Fast regular rate

        ecg_signal = np.zeros_like(t)

        # Generate wide QRS complexes at regular fast intervals
        beat_interval = 1.0 / heart_rate_hz
        beat_times = np.arange(0, t[-1], beat_interval)

        for beat_time in beat_times:
            if beat_time < t[-1]:
                # Find closest time index
                beat_idx = np.argmin(np.abs(t - beat_time))

                # Wide QRS complex (>120ms)
                qrs_width = 0.15  # seconds (wide)
                qrs_samples = int(qrs_width * self.sample_rate)
                start_idx = max(0, beat_idx - qrs_samples//2)
                end_idx = min(len(t), beat_idx + qrs_samples//2)

                if end_idx > start_idx:
                    qrs_t = t[start_idx:end_idx] - beat_time
                    # Bizarre, wide QRS morphology
                    qrs = 1.2 * (np.sin(2 * np.pi * 15 * qrs_t) * np.exp(-(qrs_t**2) / 0.005) +
                                0.5 * np.sin(2 * np.pi * 25 * qrs_t) * np.exp(-(qrs_t**2) / 0.003))
                    ecg_signal[start_idx:end_idx] += qrs

        # No P waves in VTach

        # Abnormal repolarization
        abnormal_t = 0.3 * np.sin(2 * np.pi * heart_rate_hz * t + np.pi) * (1 + 0.5 * np.sin(2 * np.pi * 3 * t))
        ecg_signal += abnormal_t

        # More electrical noise due to chaotic ventricular activity
        noise = 0.1 * np.random.randn(len(t))
        ecg_signal += noise

        # Baseline instability
        baseline_wander = 0.15 * np.sin(2 * np.pi * 0.2 * t) + 0.1 * np.sin(2 * np.pi * 0.5 * t)
        ecg_signal += baseline_wander

        return ecg_signal

    async def get_patient_info(self, patient_id: str) -> dict:
        """
        Get simulated patient information for ECG streaming.

        Args:
            patient_id: Patient identifier

        Returns:
            Dictionary with patient ECG metadata
        """
        patient_profile = await self._get_patient_profile(patient_id)

        return {
            "patient_id": patient_id,
            "patient_name": patient_profile.get("name", "Unknown"),
            "condition": patient_profile.get("condition", "normal"),
            "heart_rate_bpm": patient_profile.get("heart_rate", 72),
            "description": patient_profile.get("description", ""),
            "sample_rate": self.sample_rate,
            "signal_type": "patient_specific_synthetic",
            "leads": ["MLII"],  # Modified Lead II (common for monitoring)
            "duration_available": "infinite",
            "quality": "good"
        }
