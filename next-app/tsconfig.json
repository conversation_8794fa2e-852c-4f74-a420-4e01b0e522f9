{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "__tests__/Layout.test.jsx", "__tests__/Layout.test.jsx", "src/components/Layout.jsx", "node_modules/@types/aria-query"], "exclude": ["node_modules"]}