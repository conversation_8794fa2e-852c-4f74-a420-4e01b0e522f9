{"name": "next-app", "version": "0.1.0", "private": true, "author": "<PERSON> <<EMAIL>> (https://github.com/cuttage/)", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "export": "next export"}, "dependencies": {"@arction/lcjs": "^4.0.2", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/material": "^5.12.0", "@netlify/plugin-nextjs": "^4.37.4", "@types/aria-query": "^5.0.1", "aria-query": "^5.2.1", "next": "13.3.0", "react": "18.2.0", "react-dom": "18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@types/jest": "^29.5.0", "@types/react": "^18.0.35", "@types/react-dom": "^18.0.11", "autoprefixer": "^10.4.14", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "next-transpile-modules": "^10.0.0", "postcss": "^8.4.21", "tailwindcss": "^3.3.1", "typescript": "^5.0.4"}}