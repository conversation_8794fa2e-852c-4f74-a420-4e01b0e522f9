is there any default patients how is the default patients set 
is there a mistake or hard coding there 


I'll help you investigate the default patients setup and check for any hardcoding or mistakes. Let me search through the codebase to understand how default patients are configured.

Now let me search for more specific information about the ECG server's patient mapping and any hardcoded values:

Now let me check the database connection file to see how patient profiles are retrieved:

Let me also check if there are any configuration files or environment variables that define default patients:

Let me check the dashboard component and patient selection logic:

Based on my analysis of the codebase, I can now provide you with a comprehensive answer about the default patients setup and any potential issues or hardcoding.

## Default Patients Setup Analysis

### 1. **Database Seeding (Primary Source)**

The default patients are primarily set up through the database seeding process in `apps/server/src/db/seed.ts`. Here's what I found:

````typescript path=apps/server/src/db/seed.ts mode=EXCERPT
// 5. Create Patient Users using Better Auth signup
console.log("Creating patient users...");

const patient1Result = await auth.api.signUpEmail({
  body: {
    name: "<PERSON>",
    email: "<EMAIL>", 
    password: TEST_PASSWORD,
  },
});

// Patient profiles with hardcoded MRNs
if (patient1Result.user && createdDoctors.length > 0) {
  patientProfiles.push({
    id: nanoid(),
    userId: patient1Result.user.id,
    medicalRecordNumber: "MRN001",  // ⚠️ HARDCODED
    dateOfBirth: new Date("1985-03-15"),
    gender: "male" as const,
    emergencyContact: "Jane Smith (Wife)",
    emergencyPhone: "******-1001",
    assignedDoctorId: createdDoctors[0].id,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
}
````

### 2. **ECG Server Patient Mapping (Hardcoded Logic)**

The ECG server has **significant hardcoding** in `apps/ecg-server/src/ecg/simulator.py`:

````python path=apps/ecg-server/src/ecg/simulator.py mode=EXCERPT
# Assign conditions based on MRN or patient characteristics
if mrn == "MRN001":  # ⚠️ HARDCODED John Smith
    profile = {
        "name": name,
        "condition": "normal",
        "heart_rate": 68,
        "description": f"Healthy patient with normal sinus rhythm (MRN: {mrn})"
    }
elif mrn == "MRN002":  # ⚠️ HARDCODED Maria Garcia
    profile = {
        "name": name,
        "condition": "atrial_fibrillation", 
        "heart_rate": 95,
        "description": f"Patient with atrial fibrillation (MRN: {mrn})"
    }
elif mrn == "MRN003":  # ⚠️ HARDCODED Robert Johnson
    profile = {
        "name": name,
        "condition": "bradycardia",
        "heart_rate": 45,
        "description": f"Patient with bradycardia (MRN: {mrn})"
    }
elif mrn == "MRN-TEST-001" or patient_id == "test-patient-1":  # ⚠️ HARDCODED
    profile = {
        "name": name,
        "condition": "ventricular_tachycardia",
        "heart_rate": 180,
        "description": f"Test patient with ventricular tachycardia episodes (MRN: {mrn})"
    }
````

### 3. **Frontend Patient Selection Logic**

The web dashboard has auto-selection logic with hardcoded fallbacks:

````typescript path=apps/web/src/app/dashboard/ecg/page.tsx mode=EXCERPT
// Auto-select patient with MRN-TEST-001 if available, otherwise first patient
useEffect(() => {
  if (!selectedPatientId && patients.length > 0) {
    // Look for the test patient first by MRN or name
    const testPatient = patients.find(p => 
      p.medical_record_number === "MRN-TEST-001" ||  // ⚠️ HARDCODED
      p.name.includes("Test Patient ECG")            // ⚠️ HARDCODED
    );
    
    if (testPatient) {
      console.log("🎯 Auto-selecting test patient:", testPatient.id);
      setSelectedPatientId(testPatient.id);
    } else {
      setSelectedPatientId(patients[0].id);
    }
  }
}, [patients, selectedPatientId]);

// Fallback to test patient if no patients are loaded
useEffect(() => {
  if (!selectedPatientId && !patientsLoading && patients.length === 0) {
    console.log("🧪 Using test patient ID for debugging");
    setSelectedPatientId("QqGq_lQX8-IMQqSjE_fPV");  // ⚠️ HARDCODED ID
  }
}, [selectedPatientId, patientsLoading, patients.length]);
````

## 🚨 **Issues and Problems Identified:**

### **1. Extensive Hardcoding**
- **MRN values** are hardcoded (`MRN001`, `MRN002`, `MRN003`, `MRN-TEST-001`)
- **Patient IDs** are hardcoded (`test-patient-1`, `QqGq_lQX8-IMQqSjE_fPV`)
- **Cardiac conditions** are mapped by hardcoded MRN values
- **Patient names** are hardcoded in selection logic

### **2. Tight Coupling**
- ECG server directly depends on specific MRN values
- Frontend has hardcoded patient selection logic
- No configuration-based approach for patient profiles

### **3. Development vs Production Issues**
- Test patient IDs are mixed with production logic
- Fallback mechanisms use hardcoded values
- No clear separation between test and real data

### **4. Maintenance Problems**
- Adding new patients requires code changes in multiple places
- Cardiac condition mapping is not data-driven
- Patient characteristics are not configurable

## **Recommendations:**

1. **Move patient profiles to database configuration**
2. **Create a patient profile service** that maps conditions dynamically
3. **Use environment variables** for test patient configurations
4. **Implement a proper patient seeding system** with configurable profiles
5. **Remove hardcoded MRN-to-condition mappings** from the ECG server

The current setup works for development but is not scalable or maintainable for production use.
