// src/App.jsx
import React from 'react';
import ECGCarousel from './ECGCarousel'; // Adjust path if needed

function App() {
  // Optional: Define a custom dataStream function for live data
  const fetchLiveData = () => {
    // Replace with actual data source, e.g., WebSocket or API
    return Math.sin(Date.now() / 500) * 50 + Math.random() * 5; // Example data
  };

  return (
    <div>
      <h1>Live ECG Monitor</h1>
      <ECGCarousel dataStream={fetchLiveData} />
    </div>
  );
}

export default App;

// src/ECGCarousel.jsx
import React, { useRef, useEffect, useState } from 'react';

const ECGCarousel = ({ dataStream }) => {
  const canvasRef = useRef(null);
  const [data, setData] = useState([]);
  const width = 800;
  const height = 200;
  const gridSize = 20;
  const amplitude = 50;
  const speed = 2;

  const simulateData = () => {
    return Math.sin(Date.now() / 1000) * amplitude + Math.random() * 10 - 5;
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    const drawGrid = () => {
      ctx.strokeStyle = '#ffdddd';
      ctx.lineWidth = 0.5;
      for (let x = 0; x <= width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      for (let y = 0; y <= height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
    };

    let animationFrame;
    const render = () => {
      const newPoint = dataStream ? dataStream() : simulateData();
      setData((prev) => {
        const newData = [...prev, newPoint];
        if (newData.length > width / speed) {
          newData.shift();
        }
        return newData;
      });

      ctx.clearRect(0, 0, width, height);
      drawGrid();

      ctx.strokeStyle = '#00ff00';
      ctx.lineWidth = 2;
      ctx.beginPath();
      data.forEach((point, index) => {
        const x = index * speed;
        const y = height / 2 - point;
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      ctx.stroke();

      animationFrame = requestAnimationFrame(render);
    };

    render();

    return () => cancelAnimationFrame(animationFrame);
  }, [data, dataStream]);

  return (
    <div style={{ overflow: 'hidden', width: `${width}px`, height: `${height}px`, border: '1px solid #ccc' }}>
      <canvas ref={canvasRef} width={width} height={height} />
    </div>
  );
};

export default ECGCarousel;