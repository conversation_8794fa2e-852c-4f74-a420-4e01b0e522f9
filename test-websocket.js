#!/usr/bin/env node

/**
 * Simple WebSocket test for ECG server
 * Tests the WebSocket connection with authentication
 */

const WebSocket = require("ws");

// Test parameters
const ECG_SERVER_URL = "ws://localhost:8000";
const PATIENT_ID = "test-patient-1";
const USER_ID = "Oaczetepf8kgsv5uiTJsW5Hi52dSL4BA"; // Dr. <PERSON>'s user ID

// Create WebSocket connection
const wsUrl = `${ECG_SERVER_URL}/ws/ecg/${PATIENT_ID}?token=${USER_ID}`;

// Add more detailed debugging
console.log("🔧 WebSocket Client Configuration:");
console.log(`   - Node.js version: ${process.version}`);
console.log(`   - WebSocket library: ws`);
console.log(`   - Connection timeout: 10 seconds`);
console.log("");

console.log("🔗 Testing WebSocket connection...");
console.log(`📍 URL: ${wsUrl}`);
console.log(`👤 User ID: ${USER_ID}`);
console.log(`🏥 Patient ID: ${PATIENT_ID}`);
console.log("");

const ws = new WebSocket(wsUrl);

ws.on("open", () => {
  console.log("✅ WebSocket connection opened successfully!");
});

ws.on("message", (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log("📨 Received message:", {
      type: message.type,
      timestamp: message.timestamp,
      patient_id: message.patient_id,
      data: message.data ? Object.keys(message.data) : "no data",
    });
  } catch (err) {
    console.log("📨 Received raw message:", data.toString());
  }
});

ws.on("close", (code, reason) => {
  console.log(
    `🔌 WebSocket closed - Code: ${code}, Reason: "${
      reason || "No reason provided"
    }"`
  );

  // Close code explanations
  const closeReasons = {
    1000: "Normal closure",
    1001: "Going away",
    1002: "Protocol error",
    1003: "Unsupported data",
    1006: "Abnormal closure (no close frame)",
    1008: "Policy violation (likely authentication failed)",
    1009: "Message too big",
    1010: "Extension required",
    1011: "Internal server error",
    1012: "Service restart",
    1013: "Try again later",
    1014: "Bad gateway",
    1015: "TLS handshake failure",
  };

  const explanation = closeReasons[code] || "Unknown close code";
  console.log(`📋 Explanation: ${explanation}`);

  process.exit(code === 1000 ? 0 : 1);
});

ws.on("error", (error) => {
  console.error("❌ WebSocket error:", error.message);
  console.error("🔍 Error details:", {
    code: error.code,
    errno: error.errno,
    syscall: error.syscall,
    address: error.address,
    port: error.port,
  });
});

// Timeout after 10 seconds
setTimeout(() => {
  console.log("⏰ Test timeout - closing connection");
  ws.close();
}, 10000);
