# ECG Issue 1: Random Flatlines Analysis

## Issue Description
ECG signal display randomly breaks and shows flatline sections intermittently during normal operation.

## Root Cause Analysis

### 🔍 **Primary Causes Identified:**

#### 1. **WebSocket Connection Interruptions**
**Location**: `useECGStream.ts:230-272`

**Problem**: WebSocket connections can close unexpectedly due to:
- Network instability
- Server-side disconnections
- Authentication failures (code 1008)
- Server errors (code 1011)
- Abnormal closures (code 1006)

**Impact**: When WebSocket disconnects:
- `setIsConnected(false)` is called
- Signal data stops flowing
- Chart continues displaying last known data points
- Creates visual flatline until reconnection

```typescript
// Current handling in useECGStream.ts
ws.onclose = (event) => {
  setIsConnected(false); // ❌ Signal stops, but chart doesn't clear
  // Reconnection logic may take time
  if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
    setConnectionState("reconnecting");
    attemptReconnection(); // ❌ Delayed reconnection creates gaps
  }
};
```

#### 2. **Reconnection Delays & Buffer Underflow**
**Location**: `useECGStream.ts:302-318`

**Problem**: Exponential backoff reconnection creates increasing delays:
- Attempt 1: 1 second delay
- Attempt 2: 2 seconds delay
- Attempt 3: 4 seconds delay
- Attempt 4: 8 seconds delay
- Attempt 5: 16 seconds delay

**Impact**: During reconnection delays:
- Chart continues consuming buffered data
- Once buffer is exhausted, shows flatline
- New data arrives after reconnection with time gaps

```typescript
// Current reconnection logic
const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
setTimeout(connectToStream, delay); // ❌ Creates gaps in data
```

#### 3. **Signal Buffer Management Issues**
**Location**: `useECGStream.ts:140-146`

**Problem**: Signal buffer continues during disconnections:
- Buffer holds old data points
- No timestamp validation for stale data
- Chart displays mixed old/new data creating visual artifacts

```typescript
setSignal(prev => {
  const newSignal = [...prev, dataPoint];
  return newSignal.length > maxSignalBuffer
    ? newSignal.slice(-maxSignalBuffer) // ❌ Keeps old data during disconnects
    : newSignal;
});
```

### 🔍 **Secondary Contributing Factors:**

#### 4. **Error Handling Gaps**
**Location**: `useECGStream.ts:183-186`

**Problem**: Generic error handling doesn't differentiate between:
- Temporary network issues
- Server-side problems
- Data corruption
- Authentication failures

#### 5. **No Connection State Awareness in Chart**
**Location**: `SimpleECGChart.tsx:183-244`

**Problem**: Chart component doesn't react to connection state:
- Continues rendering during disconnections
- No visual indication of connection problems
- Doesn't clear stale data when connection lost

### 🔍 **Data Flow Analysis:**

```
ECG Server → WebSocket → useECGStream → SimpleECGChart
     ↑              ↓             ↓            ↓
Normal Flow   Connected    Signal Buffer   Chart Display
               ↓                          ↓
          Disconnect                Flatline Display
               ↓                          ↓
          Reconnect Delay            Data Gap
               ↓                          ↓
          New Data Arrives       Overlapping Display
```

### 🔍 **Specific Code Issues:**

#### Issue A: Missing Connection State in Chart
```typescript
// SimpleECGChart.tsx - Missing effect
// ❌ NO useEffect for connection state changes
// ❌ NO chart clearing on disconnect
// ❌ NO visual indicator for connection status
```

#### Issue B: Incomplete Signal Reset
```typescript
// useECGStream.ts - Incomplete cleanup
const disconnect = useCallback(() => {
  // ❌ signal state not cleared
  // ❌ classifications not cleared
  // ❌ chart continues showing old data
}, []);
```

#### Issue C: Timestamp Gaps Not Handled
```typescript
// SimpleECGChart.tsx - No timestamp validation
latestPoints.forEach((point: ECGDataPoint) => {
  const timeInSeconds = point.timestamp / 1000;
  labels.push(timeInSeconds); // ❌ No gap detection
  data.push(point.value * config.scale);
});
```

## 🚨 **Critical Issues Summary:**

1. **No chart state reset on WebSocket disconnect**
2. **Exponential backoff creates large data gaps**
3. **No timestamp validation for data continuity**
4. **Missing connection state awareness in chart component**
5. **Signal buffer accumulates stale data during disconnects**

## 💡 **Recommended Solutions:**

### High Priority:
1. **Add connection state awareness to chart component**
2. **Clear signal buffer on disconnect**
3. **Implement timestamp gap detection**
4. **Add visual indicators for connection status**

### Medium Priority:
1. **Optimize reconnection strategy**
2. **Implement data interpolation for small gaps**
3. **Add connection health monitoring**

### Low Priority:
1. **Implement offline mode with cached data**
2. **Add connection quality metrics**
3. **Implement predictive reconnection**

## 📊 **Impact Assessment:**

- **Severity**: HIGH - Affects core functionality
- **Frequency**: INTERMITTENT - Depends on network stability
- **User Impact**: HIGH - Compromises medical monitoring reliability
- **Technical Debt**: MEDIUM - Requires architectural changes

## 🔧 **Next Steps:**

1. Implement immediate fixes for connection state handling
2. Add data validation and gap detection
3. Improve user feedback during connection issues
4. Add comprehensive logging for debugging
5. Implement connection recovery strategies