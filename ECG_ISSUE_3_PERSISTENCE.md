# ECG Issue 3: Patient Data Persistence Analysis

## Issue Description
When switching from the default patient to a different patient, the default patient's ECG graph remains and gets overlapped by the new patient's ECG data instead of being completely replaced.

## Root Cause Analysis

### 🔍 **Primary Causes Identified:**

#### 1. **Chart Component State Persistence**
**Location**: `SimpleECGChart.tsx:58-73` - **CRITICAL ISSUE**

**Problem**: Chart component maintains internal state that never resets:
- `chartData` state persists across patient switches
- Chart.js datasets accumulate data indefinitely
- No useEffect hook monitors `patientId` changes

```typescript
// ❌ PERSISTENT STATE in SimpleECGChart.tsx
const [chartData, setChartData] = useState<ChartData<"line">>({
  labels: [], // These arrays persist across patient changes
  datasets: [{
    label: "ECG Signal",
    data: [], // This array accumulates all patients' data
    borderColor: "rgb(34, 197, 94)",
    backgroundColor: "rgba(34, 197, 94, 0.1)",
    borderWidth: 2,
    fill: false,
    tension: 0.1,
    pointRadius: 0,
    pointHoverRadius: 0,
  }],
});
```

**Impact**: Default patient data remains visible because:
- Chart state is initialized once and never reset
- New patient data gets added to existing datasets
- Chart renders combined data from all previously selected patients

#### 2. **Missing Patient Switch Detection**
**Location**: `SimpleECGChart.tsx` - **CRITICAL MISSING CODE**

**Problem**: Chart component has no mechanism to detect patient switches:
- No `useEffect` with `patientId` dependency
- No prop change handling
- No state reset on patient changes

```typescript
// ❌ COMPLETELY MISSING in SimpleECGChart.tsx
useEffect(() => {
  // This entire useEffect is missing
  // Should reset all chart state when patientId changes
}, [patientId]);
```

#### 3. **useECGStream Hook State Persistence**
**Location**: `useECGStream.ts:28-40`

**Problem**: Signal buffer persists across patient changes:
- Signal state is not cleared when patient switches
- Buffer contains mixed data from multiple patients
- No patient-specific data isolation

```typescript
// ❌ PERSISTENT STATE in useECGStream.ts
const [signal, setSignal] = useState<ECGDataPoint[]>([]); // Never cleared on patient switch
const [classifications, setClassifications] = useState<ClassificationResult[]>([]); // Also persists
```

**Evidence**: The signal buffer accumulates data:
```typescript
setSignal(prev => {
  const newSignal = [...prev, dataPoint]; // ❌ Always adds to existing data
  return newSignal.length > maxSignalBuffer
    ? newSignal.slice(-maxSignalBuffer)
    : newSignal;
});
```

### 🔍 **Secondary Contributing Factors:**

#### 4. **WebSocket Connection Reuse**
**Location**: `useECGStream.ts:338-373`

**Problem**: WebSocket connections may persist across patient changes:
- Connection is not properly terminated when switching patients
- Previous patient's data may continue arriving
- New patient's data gets mixed with old data

```typescript
// ❌ INCOMPLETE PATIENT SWITCH HANDLING
useEffect(() => {
  if (!patientId) return;

  connectToStream(); // ❌ Doesn't clear previous data

  return () => {
    disconnect(); // ❌ Cleanup may not clear all state
  };
}, [patientId, session?.user?.id]);
```

#### 5. **Chart.js Dataset Persistence**
**Location**: `SimpleECGChart.tsx:210-226`

**Problem**: Chart.js datasets are updated, not replaced:
- Dataset labels and data persist
- Only new data points are added
- Previous patient's waveform remains in chart memory

```typescript
setChartData((prevData) => ({
  labels, // ❌ Should be empty array for new patient
  datasets: [{
    ...prevData.datasets[0], // ❌ Preserves all existing data
    data, // ❌ Adds new data to existing data
  }],
}));
```

### 🔍 **Data Flow Analysis During Patient Switch:**

```
Initial State: Default Patient Selected
    ↓
Default Patient ECG → Signal Buffer → Chart Display
    ↓
User Clicks Different Patient
    ↓
selectedPatientId Changes (❌ No chart reset)
    ↓
New Patient ECG → Signal Buffer (❌ Adds to existing buffer)
    ↓
Chart Display (❌ Shows Default + New Patient Data)
    ↓
Result: Overlapping ECG Signals
```

### 🔍 **Specific Code Issues:**

#### Issue A: No Patient Switch Handler
```typescript
// ❌ MISSING CODE in SimpleECGChart.tsx
// Should implement:
useEffect(() => {
  // Reset all chart state when patient changes
  handlePatientSwitch();
}, [patientId]);

const handlePatientSwitch = () => {
  setChartData({
    labels: [],
    datasets: [{
      label: `ECG Signal - Patient ${patientId}`,
      data: [],
      borderColor: "rgb(34, 197, 94)",
      backgroundColor: "rgba(34, 197, 94, 0.1)",
      borderWidth: 2,
      fill: false,
      tension: 0.1,
      pointRadius: 0,
      pointHoverRadius: 0,
    }],
  });

  // Reset any other state
  setIsRecording(false);
  setConfig(prev => ({ ...prev, timeWindow: 10, maxDataPoints: 3600 }));
};
```

#### Issue B: Signal Buffer Not Cleared
```typescript
// ❌ MISSING in useECGStream.ts
useEffect(() => {
  // Should clear signal when patient changes
  setSignal([]);
  setClassifications([]);
  setError(null);
  setSessionId(null);
}, [patientId]);
```

#### Issue C: Chart Dataset Not Reset
```typescript
// ❌ CURRENT BEHAVIOR in SimpleECGChart.tsx
const processNewData = useCallback(() => {
  // Processes signal data without patient validation
  const latestPoints = signal.slice(-config.maxDataPoints);

  const labels: number[] = [];
  const data: number[] = [];

  latestPoints.forEach((point: ECGDataPoint) => {
    const timeInSeconds = point.timestamp / 1000;
    labels.push(timeInSeconds); // ❌ Accumulates timestamps
    data.push(point.value * config.scale); // ❌ Accumulates values
  });

  setChartData((prevData) => ({
    labels, // ❌ Should clear old labels
    datasets: [{
      ...prevData.datasets[0],
      data, // ❌ Should replace data, not add to it
    }],
  }));
}, [signal, config]);
```

### 🔍 **Patient Switch Event Sequence:**

1. **User clicks different patient in dashboard**
2. **`selectedPatientId` state changes**
3. **SimpleECGChart receives new `patientId` prop**
4. **❌ Chart component doesn't detect the change**
5. **❌ Chart state remains unchanged**
6. **useECGStream connects to new patient's WebSocket**
7. **New patient's ECG data arrives**
8. **❌ Data gets added to existing chart datasets**
9. **Chart displays combined ECG from default + new patient**

### 🔍 **Visual Evidence Analysis:**

Based on user description and code analysis:
- Default patient's ECG waveform remains visible
- New patient's ECG overlaps with default
- Creates confusing double-ECG display
- Makes medical interpretation impossible
- Indicates state persistence rather than replacement

## 🚨 **Critical Issues Summary:**

1. **No patient switch detection in chart component** (CRITICAL)
2. **Chart state never resets on patient changes** (CRITICAL)
3. **Signal buffer accumulates multiple patients' data** (CRITICAL)
4. **Missing useEffect for patientId changes** (HIGH)
5. **Incomplete WebSocket cleanup on patient switch** (HIGH)

## 💡 **Recommended Solutions:**

### High Priority (Critical Fixes):
1. **Add patient switch detection useEffect**
   ```typescript
   useEffect(() => {
     // Reset all chart state
     resetChartState();
   }, [patientId]);
   ```

2. **Implement signal buffer clearing**
   ```typescript
   useEffect(() => {
     setSignal([]);
     setClassifications([]);
   }, [patientId]);
   ```

3. **Add complete chart state reset**
   ```typescript
   const resetChartState = () => {
     setChartData({
       labels: [],
       datasets: [createFreshDataset(patientId)],
     });
   };
   ```

### Medium Priority:
1. **Add patient-specific chart labels**
2. **Implement smooth patient transitions**
3. **Add loading states during patient switches**

### Low Priority:
1. **Add patient history tracking**
2. **Implement patient comparison features**
3. **Add patient-specific chart configurations**

## 📊 **Impact Assessment:**

- **Severity**: CRITICAL - Renders patient monitoring unusable
- **Frequency**: CONSISTENT - Happens on every patient switch
- **User Impact**: CRITICAL - Makes accurate monitoring impossible
- **Medical Risk**: HIGH - Could lead to incorrect patient assessment

## 🔧 **Next Steps:**

1. **Immediate**: Add patient switch useEffect
2. **Urgent**: Implement chart state reset
3. **Short-term**: Clear signal buffers
4. **Medium-term**: Add patient-specific data validation
5. **Long-term**: Implement patient context awareness

## 🧪 **Testing Strategy:**

1. **Test patient switching with different ECG patterns**
2. **Verify complete chart state reset**
3. **Check signal buffer isolation**
4. **Validate WebSocket cleanup**
5. **Test rapid patient switching scenarios**

## 🎯 **Success Criteria:**

- Chart displays only current patient's ECG
- No data persistence between patients
- Clean transitions between patients
- Clear visual indication of current patient
- Reliable patient switching functionality